import 'dart:convert';
import 'dart:io';

Future<void> main() async {
  final config = {
    'BASE_URL': Platform.environment['baseUrl'],
    'GOOGLE_API_KEY': Platform.environment['googleApiKey'],
    'ENVIRONMENT': Platform.environment['environment'],
  };

  const kFileName = 'lib/.env.dart';
  final file = await File(kFileName).create(recursive: true);
  if (file.existsSync()) {
    file.writeAsString("final appConfig = ${json.encode(config)};");
  }
}
