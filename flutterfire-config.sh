#!/bin/bash
# Script to generate Firebase configuration files for different environments/flavors

if [[ $# -eq 0 ]]; then
  echo "Error: No environment specified. Use 'dev', or 'prod'."
  exit 1
fi

case $1 in
  dev)
    flutterfire config \
      --project=luxletdev \
      --out=lib/firebase_config/firebase_options_dev.dart \
      --ios-bundle-id=com.com.luxlet.fleetmgr.dev \
      --ios-out=ios/flavors/dev/GoogleService-Info.plist \
      --android-package-name=com.luxlet.fleetmgr.dev \
      --android-out=android/app/src/dev/google-services.json
    ;;
  prod)
    flutterfire config \
      --project=secapay-pos \
      --out=lib/firebase_config/firebase_options_prod.dart \
      --ios-bundle-id=com.luxlet.fleetmgr \
      --ios-out=ios/flavors/prod/GoogleService-Info.plist \
      --android-package-name=com.luxlet.fleetmgr \
      --android-out=android/app/src/prod/google-services.json
    ;;
  *)
    echo "Error: Invalid environment specified. Use 'dev', or 'prod'."
    exit 1
    ;;
esac
