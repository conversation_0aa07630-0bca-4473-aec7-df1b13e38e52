name: fleet_mobile
description: 'A new Flutter project.'

# Prevent accidental publishing to pub.dev.
publish_to: 'none'

version: 1.0.0+5

environment:
  sdk: '>=3.4.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  riverpod: ^2.6.1
  flutter_riverpod: ^2.6.1
  dio: ^5.8.0+1
  multiple_result: ^5.2.0
  go_router: ^15.1.2
  bot_toast: ^4.1.3
  page_transition: ^2.2.1
  dots_indicator: ^4.0.1
  smooth_page_indicator: ^1.2.1
  wc_form_validators: ^1.1.0
  flutter_svg: ^2.1.0
  cached_network_image: ^3.4.1
  intl: ^0.20.0
  auto_size_text: ^3.0.0
  dropdown_textfield: ^1.2.0
  device_info_plus: ^11.4.0
  flutter_secure_storage: ^9.2.4
  url_launcher: ^6.3.1
  firebase_core: ^3.10.0
  firebase_messaging: ^15.2.1
  flutter_local_notifications: ^19.02.1
  pinput: ^5.0.1
  flutter_google_places_sdk: ^0.4.2+1
  #   git:
  #     url: https://github.com/fluttercommunity/flutter_google_places
  #     ref: v0.3.2
  equatable: ^2.0.7
  image_picker: ^1.1.2
  audioplayers: ^6.4.0
  skeletonizer: ^2.0.1
  photo_view: ^0.15.0
  pretty_dio_logger: ^1.4.0
  cote_network_logger: ^1.0.4

dependency_overrides:
  # intl: ^0.18.0
  # web: ^0.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  # Enable generation of localized Strings from arb files.
  generate: true

  assets:
    # Add assets from the images directory to the application.
    - assets/images/
    - assets/svgs/
    - assets/sound/

  fonts:
    - family: AvenirNextCyr
      fonts:
        - asset: assets/fonts/AvenirNextCyr/AvenirNextCyr-Thin.ttf
          weight: 100
        - asset: assets/fonts/AvenirNextCyr/AvenirNextCyr-UltraLight.ttf
          weight: 200
        - asset: assets/fonts/AvenirNextCyr/AvenirNextCyr-Light.ttf
          weight: 300
        - asset: assets/fonts/AvenirNextCyr/AvenirNextCyr-Regular.ttf
          weight: 400
        - asset: assets/fonts/AvenirNextCyr/AvenirNextCyr-Medium.ttf
          weight: 500
        - asset: assets/fonts/AvenirNextCyr/AvenirNextCyr-Bold.ttf
          weight: 600
        - asset: assets/fonts/AvenirNextCyr/AvenirNextCyr-Heavy.ttf
          weight: 700
