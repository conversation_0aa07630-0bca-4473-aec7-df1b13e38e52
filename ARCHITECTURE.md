# LuxLet Fleet Mobile - Architecture

This document outlines the architecture of the LuxLet Fleet Mobile application, explaining the design decisions, patterns, and organization of the codebase.

## Table of Contents

1. [Overview](#overview)
2. [Core Architecture](#core-architecture)
3. [Directory Structure](#directory-structure)
4. [Feature Modules](#feature-modules)
5. [State Management](#state-management)
6. [Navigation](#navigation)
7. [Data Flow](#data-flow)
8. [Error Handling](#error-handling)
9. [Environment Configuration](#environment-configuration)
10. [Firebase Integration](#firebase-integration)
11. [Dependency Injection](#dependency-injection)
12. [Testing Strategy](#testing-strategy)

## Overview

LuxLet Fleet Mobile is built using Flutter and follows a feature-first architecture with a clear separation of concerns. The application is designed to be maintainable, scalable, and testable.

## Core Architecture

The application follows a layered architecture with the following components:

- **Presentation Layer**: UI components (screens, widgets)
- **Business Logic Layer**: Controllers and state management
- **Service Layer**: Services that handle business logic
- **Repository Layer**: Data access and API communication
- **Model Layer**: Data models and entities

## Directory Structure

```
lib/
├── core/           # Core utilities, widgets, and services
│   ├── assets/     # Asset management
│   ├── models/     # Shared data models
│   ├── routes/     # Navigation routes
│   ├── screens/    # Common screens
│   ├── utils/      # Utility functions and constants
│   └── widgets/    # Reusable widgets
├── features/       # Feature modules
│   ├── auth/       # Authentication
│   ├── booking/    # Booking management
│   ├── fleet/      # Fleet management
│   └── admin_dashboard/ # Admin capabilities
├── firebase_config/# Firebase configuration
├── l10n/           # Localization
├── theme/          # App theming
└── main.dart       # Entry point
```

## Feature Modules

Each feature is organized in its own directory with a consistent structure:

```
features/feature_name/
├── screens/        # UI components
├── widgets/        # Feature-specific widgets
├── feature_controller.dart  # State management
├── feature_service.dart     # Business logic
├── feature_repository.dart  # Data access
├── feature_state.dart       # State definition
└── models/                  # Feature-specific models
```

### Key Features

1. **Authentication**
   - User registration and login
   - Password management
   - Profile management

2. **Fleet Management**
   - Car listing and details
   - Car availability management
   - Image management for vehicles

3. **Booking Management**
   - View and manage booking requests
   - Approve/reject bookings
   - Booking history

4. **Admin Dashboard**
   - User management
   - Admin controls
   - System configuration

## State Management

The application uses Riverpod for state management with a controller-based approach:

- **Controllers**: Extend `StateNotifier` to handle business logic and state updates
- **State**: Immutable state classes that represent the current state of a feature
- **Providers**: Riverpod providers that expose controllers and state to the UI

Example flow:
```dart
// State definition
class FleetState {
  final AsyncValue<List<Car>> cars;
  final Car? carInView;
  
  // ...
}

// Controller
class FleetController extends StateNotifier<FleetState> {
  FleetController(super.state, this._fleetService);
  
  final FleetService _fleetService;
  
  Future<void> loadCars() async {
    // Update state, call service methods, etc.
  }
  
  // ...
}

// Provider
final fleetControllerProvider = StateNotifierProvider<FleetController, FleetState>(
  (ref) {
    final fleetService = ref.watch(fleetServiceProvider);
    return FleetController(
      FleetState.initial(),
      fleetService,
    );
  },
);
```

## Navigation

The application uses GoRouter for navigation, with routes defined in `lib/core/routes/router.dart`. This provides:

- Declarative routing
- Deep linking support
- Navigation guards for authentication
- Transition animations

## Data Flow

1. UI events trigger controller methods
2. Controllers process logic and update state through services
3. Services communicate with repositories
4. Repositories handle API calls and data persistence
5. Results flow back up the chain
6. UI rebuilds based on the new state

## Error Handling

The application uses the `Multiple_Result` pattern for error handling:

```dart
Future<Result<Car, Failure>> getCar(String carId) async {
  try {
    final res = await _fleetRepository.getCar(carId);
    return Success(res);
  } on Failure catch (e) {
    return Error(e);
  }
}
```

This provides clear success/failure paths and type-safe error handling.

## Environment Configuration

The application supports multiple environments (development and production) with different configurations:

- Firebase configurations per environment
- API endpoints per environment
- Feature flags

## Firebase Integration

The application integrates with Firebase for:

- Push notifications
- Analytics
- Crash reporting

## Dependency Injection

Riverpod is used for dependency injection, providing a clean way to manage dependencies and facilitate testing.

## Testing Strategy

The application is designed to be testable at all layers:

- Unit tests for services and repositories
- Widget tests for UI components
- Integration tests for feature flows

Each layer is designed with testability in mind, with clear interfaces and separation of concerns.
