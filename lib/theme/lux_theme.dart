import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/material.dart';

export 'theme_extensions.dart';

// Extension to easily access custom colors from the theme
extension CustomColorScheme on ColorScheme {
  // Bronze
  Color get bronze => const Color(0xff774900);

  // Bronze500
  Color get bronze500 => const Color(0xffffb800);

  // Blond
  Color get blond => const Color(0xffffeabc);

  // Dark900
  Color get dark900 => const Color(0xff040302);

  // Dark800
  Color get dark800 => const Color(0xff160E08);

  // Dark700
  Color get dark700 => const Color(0xff1C120B);

  // Dark600
  Color get dark600 => const Color(0xff24180E);

  // Dark500
  Color get dark500 => const Color(0xff281A0F);

  // Black
  Color get black => const Color(0xff1d1c1b);

  // White
  Color get white => const Color(0xffffffff);

  // Red500
  Color get red500 => const Color(0xffE82139);

  // Additional colors
  Color get darkGrey => const Color(0xff0C0C0C);
  Color get errorRed => const Color(0xFFCF3333);
  Color get darkBlack => const Color(0xFF121212);
  Color get pureBlack => const Color(0xFF000000);
  Color get lightGrey => const Color(0xFFEEEEEE);
  Color get mediumGrey => const Color(0xFFBDBDBD);
  Color get lightSilver => const Color(0xFFE4E4E7);
  Color get paleGreen => const Color(0xFFE9F9EF);
  Color get successGreen => const Color(0xFF22C55E);
  Color get brightGreen => const Color(0xFF33CE6D);
  Color get offWhite => const Color(0xFFEAE8E7);
  Color get charcoal => const Color(0xFF2D2C2B);
}

class LuxTheme {
  // Define gradients
  static const Gradient splashGradient = LinearGradient(
    colors: [Color(0xff774A00), Color(0xff0B0704)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0.0, 0.88],
  );

  static const Gradient landingGradient = LinearGradient(
    colors: [
      Color(0xff040302),
      Color(0xff040302),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // Dark theme colors
  static const Color darkPrimary = Color(0xff040302); // dark900
  static const Color darkOnPrimary = Color(0xffffeabc); // blond
  static const Color darkSecondary = Color(0xffffeabc); // blond
  static const Color darkOnSecondary = Color(0xff040302); // dark900
  static const Color darkSurface = Color(0xff1d1c1b); // black
  static const Color darkOnSurface = Color(0xffffffff); // white
  static const Color darkTertiary = Color(0xffffb800); // bronze500
  static const Color darkError = Color(0xffE82139); // red500

  // Light theme colors
  static const Color lightPrimary = Color(0xffffeabc); // blond
  static const Color lightOnPrimary = Color(0xff281A0F); // dark500
  static const Color lightSecondary = Color(0xff774900); // bronze
  static const Color lightOnSecondary = Color(0xffffffff); // white
  static const Color lightSurface = Color(0xffFAF7F2); // light cream
  static const Color lightOnSurface = Color(0xff281A0F); // dark500
  static const Color lightTertiary = Color(0xffffb800); // bronze500
  static const Color lightError = Color(0xffE82139); // red500
  static const Color lightBackground = Color(0xffFFFDF9); // off-white

  /// Creates the dark theme for the app
  static ThemeData darkTheme(BuildContext context) {
    final ThemeData base = ThemeData.dark(useMaterial3: true);
    return base.copyWith(
      scaffoldBackgroundColor: darkPrimary,
      colorScheme: base.colorScheme.copyWith(
        primary: darkPrimary,
        onPrimary: darkOnPrimary,
        secondary: darkSecondary,
        onSecondary: darkOnSecondary,
        error: darkError,
        surface: darkSurface,
        onSurface: darkOnSurface,
        surfaceTint: darkPrimary,
        tertiary: darkTertiary,
        onTertiary: darkPrimary,
      ),
      textTheme: _buildDarkTheme(base.textTheme),
      textSelectionTheme: const TextSelectionThemeData(
        selectionColor: darkPrimary,
        cursorColor: darkSecondary,
        selectionHandleColor: darkSecondary,
      ),
      appBarTheme: const AppBarTheme(
        foregroundColor: darkOnSurface,
        backgroundColor: darkPrimary,
        elevation: 0,
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: darkSecondary,
        ),
      ),
      // elevatedButtonTheme: ElevatedButtonThemeData(
      //   style: ElevatedButton.styleFrom(
      //     backgroundColor: darkSecondary,
      //     foregroundColor: darkOnSecondary,
      //     disabledBackgroundColor: darkSecondary.withAlpha(102), // 0.4 opacity
      //     shape: RoundedRectangleBorder(
      //       borderRadius: BorderRadius.circular(50),
      //     ),
      //   ),
      // ),
      // outlinedButtonTheme: OutlinedButtonThemeData(
      //   style: OutlinedButton.styleFrom(
      //     foregroundColor: darkSecondary,
      //     side: const BorderSide(color: darkSecondary),
      //     shape: RoundedRectangleBorder(
      //       borderRadius: BorderRadius.circular(50),
      //     ),
      //   ),
      // ),
      inputDecorationTheme: const InputDecorationTheme(
        fillColor: darkSurface,
        border: CutCornersBorder(),
        focusedBorder: CutCornersBorder(
          borderSide: BorderSide(
            width: 2.0,
            color: darkSecondary,
          ),
        ),
        floatingLabelStyle: TextStyle(
          color: darkSecondary,
        ),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: darkTertiary,
      ),
      dividerTheme: const DividerThemeData(
        color: darkSecondary,
        thickness: 1,
      ),
      cardTheme: CardThemeData(
        color: const Color(0xff160E08), // dark800
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: const BorderSide(color: darkSecondary),
        ),
      ),
    );
  }

  /// Creates the light theme for the app
  static ThemeData lightTheme(BuildContext context) {
    final ThemeData base = ThemeData.light(useMaterial3: true);
    return base.copyWith(
      scaffoldBackgroundColor: lightBackground,
      colorScheme: base.colorScheme.copyWith(
        primary: lightPrimary,
        onPrimary: lightOnPrimary,
        secondary: lightSecondary,
        onSecondary: lightOnSecondary,
        error: lightError,
        surface: lightSurface,
        onSurface: lightOnSurface,
        surfaceTint: lightPrimary,
        tertiary: lightTertiary,
        onTertiary: darkPrimary,
      ),
      textTheme: _buildLightTheme(base.textTheme),
      textSelectionTheme: TextSelectionThemeData(
        selectionColor: lightPrimary.withAlpha(128),
        cursorColor: lightSecondary,
        selectionHandleColor: lightSecondary,
      ),
      appBarTheme: const AppBarTheme(
        foregroundColor: lightOnPrimary,
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: lightOnPrimary),
        titleTextStyle: TextStyle(
          color: lightOnPrimary,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          fontFamily: 'AvenirNextCyr',
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: lightSecondary,
        ),
      ),
      // elevatedButtonTheme: ElevatedButtonThemeData(
      //   style: ElevatedButton.styleFrom(
      //     backgroundColor: lightSecondary,
      //     foregroundColor: lightOnSecondary,
      //     disabledBackgroundColor: lightSecondary.withAlpha(102), // 0.4 opacity
      //     shape: RoundedRectangleBorder(
      //       borderRadius: BorderRadius.circular(50),
      //     ),
      //   ),
      // ),
      // outlinedButtonTheme: OutlinedButtonThemeData(
      //   style: OutlinedButton.styleFrom(
      //     foregroundColor: lightSecondary,
      //     side: const BorderSide(color: lightSecondary),
      //     shape: RoundedRectangleBorder(
      //       borderRadius: BorderRadius.circular(50),
      //     ),
      //   ),
      // ),
      inputDecorationTheme: InputDecorationTheme(
        fillColor: lightSurface,
        border: const CutCornersBorder(),
        focusedBorder: const CutCornersBorder(
          borderSide: BorderSide(
            width: 2.0,
            color: lightSecondary,
          ),
        ),
        floatingLabelStyle: const TextStyle(
          color: lightSecondary,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: lightOnPrimary),
        ),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: lightTertiary,
      ),
      dividerTheme: const DividerThemeData(
        color: lightSecondary,
        thickness: 1,
      ),
      cardTheme: CardThemeData(
        color: lightSurface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: const BorderSide(color: lightSecondary),
        ),
      ),
    );
  }
}

TextTheme _buildDarkTheme(TextTheme base) {
  return base
      .copyWith(
        // Display styles
        displayLarge: base.displayLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 48,
          color: LuxTheme.darkOnSurface, // white
        ),
        displayMedium: base.displayMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 36,
          color: LuxTheme.darkOnSurface, // white
        ),
        displaySmall: base.displaySmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 30,
          color: LuxTheme.darkOnSurface, // white
        ),

        // Headline styles
        headlineLarge: base.headlineLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 28,
          color: LuxTheme.darkOnSurface, // white
        ),
        headlineMedium: base.headlineMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 24,
          color: LuxTheme.darkOnSurface, // white
        ),
        headlineSmall: base.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 22,
          color: LuxTheme.darkOnSurface, // white
        ),

        // Title styles
        titleLarge: base.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 20,
          color: LuxTheme.darkOnSurface, // white
        ),
        titleMedium: base.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 18,
          color: LuxTheme.darkOnSurface, // white
        ),
        titleSmall: base.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 16,
          color: LuxTheme.darkOnSurface, // white
        ),

        // Body styles
        bodyLarge: base.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          color: LuxTheme.darkOnSurface, // white
        ),
        bodyMedium: base.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          color: LuxTheme.darkOnSurface, // white
        ),
        bodySmall: base.bodySmall?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 12,
          color: LuxTheme.darkOnSurface, // white
        ),

        // Label styles
        labelLarge: base.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: LuxTheme.darkOnSurface, // white
        ),
        labelMedium: base.labelMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 12,
          color: LuxTheme.darkOnSurface, // white
        ),
        labelSmall: base.labelSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 10,
          color: LuxTheme.darkOnSurface, // white
        ),
      )
      .apply(
        fontFamily: 'AvenirNextCyr',
      );
}

TextTheme _buildLightTheme(TextTheme base) {
  return base
      .copyWith(
        // Display styles
        displayLarge: base.displayLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 48,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        displayMedium: base.displayMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 36,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        displaySmall: base.displaySmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 30,
          color: LuxTheme.lightOnSurface, // dark500
        ),

        // Headline styles
        headlineLarge: base.headlineLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 28,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        headlineMedium: base.headlineMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 24,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        headlineSmall: base.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 22,
          color: LuxTheme.lightOnSurface, // dark500
        ),

        // Title styles
        titleLarge: base.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 20,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        titleMedium: base.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 18,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        titleSmall: base.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 16,
          color: LuxTheme.lightOnSurface, // dark500
        ),

        // Body styles
        bodyLarge: base.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        bodyMedium: base.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        bodySmall: base.bodySmall?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 12,
          color: LuxTheme.lightOnSurface, // dark500
        ),

        // Label styles
        labelLarge: base.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        labelMedium: base.labelMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 12,
          color: LuxTheme.lightOnSurface, // dark500
        ),
        labelSmall: base.labelSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 10,
          color: LuxTheme.lightOnSurface, // dark500
        ),
      )
      .apply(
        fontFamily: 'AvenirNextCyr',
      );
}
