import 'package:flutter/material.dart';

/// Extension methods to make it easier to access theme properties
extension ThemeExtensions on BuildContext {
  /// Get the current theme
  ThemeData get theme => Theme.of(this);

  /// Get the current color scheme
  ColorScheme get colorScheme => theme.colorScheme;

  /// Get the current text theme
  TextTheme get textTheme => theme.textTheme;

  /// Get the current app bar theme
  AppBarTheme get appBarTheme => theme.appBarTheme;

  /// Get the current input decoration theme
  InputDecorationTheme get inputDecorationTheme => theme.inputDecorationTheme;
}

/// Extension methods for ColorScheme to access custom colors
extension ColorSchemeExtensions on ColorScheme {
  /// Get the primary color with opacity
  Color primaryWithOpacity(double opacity) =>
      primary.withValues(alpha: opacity);

  /// Get the secondary color with opacity
  Color secondaryWithOpacity(double opacity) =>
      secondary.withValues(alpha: opacity);

  /// Get the surface color with opacity
  Color surfaceWithOpacity(double opacity) =>
      surface.withValues(alpha: opacity);
}

/// Usage examples:
/// 
/// ```dart
/// // Instead of:
/// final theme = Theme.of(context);
/// final colorScheme = theme.colorScheme;
/// final textTheme = theme.textTheme;
/// 
/// // You can use:
/// final colorScheme = context.colorScheme;
/// final textTheme = context.textTheme;
/// 
/// // And to access custom colors:
/// final bronze = colorScheme.bronze;
/// final blond = colorScheme.blond;
/// ```
