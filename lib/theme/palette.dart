import 'package:flutter/rendering.dart';

// This class is now deprecated. Use Theme.of(context).colorScheme instead.
// The CustomColorScheme extension in lux_theme.dart provides access to all these colors.
/*
class Palette {
  // [COLORS]
  /// bronze
  static const Color k774900 = Color(0xff774900);

  /// bronze500
  static const Color kFFB800 = Color(0xffffb800);

  /// blond
  static const Color kFFEABC = Color(0xffffeabc);

  /// dark900
  static const Color k040302 = Color(0xff040302);

  /// dark800
  static const Color k160E08 = Color(0xff160E08);

  /// dark 700
  static const Color k1C120B = Color(0xff1C120B);

  /// dark600
  static const Color k24180E = Color(0xff24180E);

  /// dark500
  static const Color k281A0F = Color(0xff281A0F);

  /// black
  static const Color k1D1C1B = Color(0xff1d1c1b);

  /// white
  static const Color kFFFFFF = Color(0xffffffff);

  /// red500
  static const Color kE82139 = Color(0xffE82139);

  static const Color k0C0C0C = Color(0xff0C0C0C);

  static const Color kCF3333 = Color(0xFFCF3333);

  static const Color k121212 = Color(0xFF121212);

  static const Color k000000 = Color(0xFF000000);

  static const Color kEEEEEE = Color(0xFFEEEEEE);

  static const Color kBDBDBD = Color(0xFFBDBDBD);

  static const Color kE4E4E7 = Color(0xFFE4E4E7);

  static const Color kE9F9EF = Color(0xFFE9F9EF);

  static const Color k22C55E = Color(0xFF22C55E);

  static const Color k33CE6D = Color(0xFF33CE6D);

  static const Color kEAE8E7 = Color(0xFFEAE8E7);

  static const Color k2D2C2B = Color(0xFF2D2C2B);

  // [GRADIENTS]
  static const Gradient splashGradient = LinearGradient(
    colors: [Color(0xff774A00), Color(0xff0B0704)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0.0, 0.88],
  );

  static const Gradient landingGradient = LinearGradient(
    colors: [
      Color(0xff040302),
      Color(0xff040302),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
*/

// Temporary class to maintain backward compatibility
// This will be removed in a future update
class Palette {
  // [COLORS]
  /// bronze
  static const Color k774900 = Color(0xff774900);

  /// bronze500
  static const Color kFFB800 = Color(0xffffb800);

  /// blond
  static const Color kFFEABC = Color(0xffffeabc);

  /// dark900
  static const Color k040302 = Color(0xff040302);

  /// dark800
  static const Color k160E08 = Color(0xff160E08);

  /// dark 700
  static const Color k1C120B = Color(0xff1C120B);

  /// dark600
  static const Color k24180E = Color(0xff24180E);

  /// dark500
  static const Color k281A0F = Color(0xff281A0F);

  /// black
  static const Color k1D1C1B = Color(0xff1d1c1b);

  /// white
  static const Color kFFFFFF = Color(0xffffffff);

  /// red500
  static const Color kE82139 = Color(0xffE82139);

  static const Color k0C0C0C = Color(0xff0C0C0C);

  static const Color kCF3333 = Color(0xFFCF3333);

  static const Color k121212 = Color(0xFF121212);

  static const Color k000000 = Color(0xFF000000);

  static const Color kEEEEEE = Color(0xFFEEEEEE);

  static const Color kBDBDBD = Color(0xFFBDBDBD);

  static const Color kE4E4E7 = Color(0xFFE4E4E7);

  static const Color kE9F9EF = Color(0xFFE9F9EF);

  static const Color k22C55E = Color(0xFF22C55E);

  static const Color k33CE6D = Color(0xFF33CE6D);

  static const Color kEAE8E7 = Color(0xFFEAE8E7);

  static const Color k2D2C2B = Color(0xFF2D2C2B);

  // [GRADIENTS]
  static const Gradient splashGradient = LinearGradient(
    colors: [Color(0xff774A00), Color(0xff0B0704)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0.0, 0.88],
  );

  static const Gradient landingGradient = LinearGradient(
    colors: [
      Color(0xff040302),
      Color(0xff040302),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
