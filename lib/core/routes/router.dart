import 'package:bot_toast/bot_toast.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/screens/screens.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/auth/auth_params.dart';
import 'package:fleet_mobile/features/auth/auth_state.dart';
import 'package:fleet_mobile/features/auth/screens/screens.dart';
import 'package:fleet_mobile/features/booking/screens/screens.dart';
import 'package:fleet_mobile/features/fleet/screens/screens.dart';
import 'package:fleet_mobile/features/fleet/screens/update_car_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:page_transition/page_transition.dart';

class AppRouter {
  AppRouter(this.ref);

  final Ref ref;

  static final _rootNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'root');

  late final router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    redirectLimit: 5,
    debugLogDiagnostics: true,
    // refreshListenable: GoRouterNotifier(ref),
    observers: [BotToastNavigatorObserver()],
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        name: splashPath,
        pageBuilder: (context, state) => CustomTransitionPage(
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return PageTransition(child: child, type: PageTransitionType.fade)
                .child!;
          },
          child: SplashScreen(state.uri.path),
        ),
      ),
      GoRoute(
        path: '/404',
        name: 'Error',
        pageBuilder: (context, state) => const MaterialPage(
          child: NotFoundScreen(),
        ),
      ),
      GoRoute(
        path: loginPath,
        name: loginPath,
        pageBuilder: (context, state) => MaterialPage(
          child: LoginScreen(),
        ),
      ),
      GoRoute(
        path: signUpPath,
        name: signUpPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: SignUpScreen(),
        ),
      ),
      GoRoute(
        path: forgotPasswordPath,
        name: forgotPasswordPath,
        pageBuilder: (context, state) => MaterialPage(
          child: ForgotPasswordScreen(),
        ),
      ),
      GoRoute(
        path: resetPasswordPath,
        name: resetPasswordPath,
        pageBuilder: (context, state) => MaterialPage(
          child: ResetPasswordScreen(state.extra as String), // the authToken
        ),
      ),
      GoRoute(
        path: changePasswordPath,
        name: changePasswordPath,
        pageBuilder: (context, state) => MaterialPage(
          child: ChangePasswordScreen(),
        ),
      ),
      GoRoute(
        path: verifyOtpPath,
        name: verifyOtpPath,
        pageBuilder: (context, state) => MaterialPage(
          child: VerifyOtpScreen(state.extra as EmailOrPhoneParams),
        ),
      ),
      GoRoute(
        path: homePath,
        name: homePath,
        pageBuilder: (context, state) => const MaterialPage(
          child: HomeScreen(),
        ),
      ),
      GoRoute(
        path: adminDashboardPath,
        name: adminDashboardPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: AdminDashboard(),
        ),
      ),
      GoRoute(
        path: adminUsersPath,
        name: adminUsersPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: UserListScreen(),
        ),
      ),
      GoRoute(
        path: adminAdminsPath,
        name: adminAdminsPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: AdminListScreen(),
        ),
      ),
      GoRoute(
        path: adminSuperAdminsPath,
        name: adminSuperAdminsPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: SuperAdminListScreen(),
        ),
      ),
      GoRoute(
        path: adminUserCarsPath,
        name: adminUserCarsPath,
        pageBuilder: (context, state) => MaterialPage(
          child: UserCarsScreen(
            userId: state.pathParameters['userId']!,
            user: state.extra as User,
          ),
        ),
      ),
      GoRoute(
        path: advertListPath,
        name: advertListPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: AdvertListScreen(),
        ),
      ),
      GoRoute(
        path: adminBrandsPath,
        name: adminBrandsPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: BrandManagementScreen(),
        ),
      ),
      GoRoute(
        path: adminCategoriesPath,
        name: adminCategoriesPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: CategoryManagementScreen(),
        ),
      ),
      GoRoute(
        path: adminBookingsPath,
        name: adminBookingsPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: AdminBookingsScreen(),
        ),
      ),
      GoRoute(
        path: adminBookingMetaPath,
        name: adminBookingMetaPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: BookingMetaScreen(),
        ),
      ),
      GoRoute(
        path: addCarPath,
        name: addCarPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: AddCarScreen(),
        ),
      ),
      GoRoute(
        path: uploadCarImagesPath,
        name: uploadCarImagesPath,
        pageBuilder: (context, state) => MaterialPage(
          child: UploadCarPhotosScreen(state.extra as String),
        ),
      ),
      GoRoute(
        path: updateProfilePath,
        name: updateProfilePath,
        pageBuilder: (context, state) => const MaterialPage(
          child: UpdateProfileScreen(),
        ),
      ),
      GoRoute(
        path: addBankAccountPath,
        name: addBankAccountPath,
        pageBuilder: (context, state) => MaterialPage(
          child: AddBankAccount(),
        ),
      ),
      GoRoute(
        path: carListingPath,
        name: carListingPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: CarListingScreen(),
        ),
      ),
      GoRoute(
        path: carDetailPath,
        name: carDetailPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: CarDetailsScreen(),
        ),
      ),
      GoRoute(
        path: carImagesPath,
        name: carImagesPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: CarImagesScreen(),
        ),
      ),
      GoRoute(
        path: updateCarPath,
        name: updateCarPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: UpdateCarInfoScreen(),
        ),
      ),
      GoRoute(
        path: bookingsPath,
        name: bookingsPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: BookingsScreen(),
        ),
      ),
      GoRoute(
        path: bookingDetailPath,
        name: bookingDetailPath,
        pageBuilder: (context, state) => const MaterialPage(
          child: BookingDetailScreen(),
        ),
      ),
    ],
    redirect: (context, state) {
      final authState = ref.read(authControllerProvider);
      final isLoggedIn = authState.isLoggedIn;
      final isOpenRoute = openRoutes.contains(state.matchedLocation);

      if (isLoggedIn && isOpenRoute) {
        return homePath;
      }

      if (!isLoggedIn && !isOpenRoute) {
        return loginPath;
      }

      return null;
    },
    errorPageBuilder: (context, state) {
      return const MaterialPage(
        child: NotFoundScreen(),
      );
    },
  );
}

const List<String> openRoutes = [
  "/",
  "/404",
  loginPath,
  signUpPath,
  forgotPasswordPath,
  verifyOtpPath,
  resetPasswordPath,
];

class GoRouterNotifier extends ChangeNotifier {
  final Ref ref;

  GoRouterNotifier(this.ref) {
    ref.listen<AuthState>(authControllerProvider, (_, __) => notifyListeners());
  }
}
