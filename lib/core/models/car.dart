import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:fleet_mobile/core/core.dart';

class Car extends Equatable {
  final String id;
  final String model;
  final Schedule? schedule;
  final num dailyPrice;
  final num dailyMinPrice;
  final int ratingSummary;
  final int bookingCount;
  final String brand;
  // final Category category;
  final num totalEarned;
  final bool isAvailable;
  final List<CarImage> images;
  final LuxAddress? address;
  final int doorCount;
  final bool hasAirCondition;
  final num acceleration;
  final num topSpeed;
  final List<Review> reviews;
  final List<BookedDate> bookedDates;
  final String category;
  final String year;
  final num carToPickupDistanceKm;
  final num pickupToDestinationDistanceKm;
  final num totalDistanceKm;

  const Car({
    required this.id,
    required this.model,
    this.schedule,
    required this.dailyPrice,
    required this.dailyMinPrice,
    required this.ratingSummary,
    required this.bookingCount,
    required this.brand,
    required this.totalEarned,
    required this.isAvailable,
    required this.images,
    required this.address,
    required this.doorCount,
    required this.hasAirCondition,
    required this.acceleration,
    required this.topSpeed,
    required this.reviews,
    required this.bookedDates,
    required this.category,
    required this.year,
    required this.carToPickupDistanceKm,
    required this.pickupToDestinationDistanceKm,
    required this.totalDistanceKm,
  });

  factory Car.defaultValue() {
    final fakeImages = List.filled(5, CarImage.defaultImage());
    return Car(
        id: randomString(10),
        model: 'default_model',
        schedule: Schedule.dayNight,
        dailyPrice: 0,
        dailyMinPrice: 0,
        ratingSummary: 0,
        bookingCount: 0,
        brand: 'Lexus',
        // category: Category.defaultCategory(),
        totalEarned: 0,
        isAvailable: false,
        images: fakeImages,
        address: LuxAddress(),
        doorCount: 4,
        hasAirCondition: true,
        acceleration: 30,
        topSpeed: 50,
        reviews: List.filled(
          10,
          Review.defaultReview(),
        ),
        bookedDates: List.filled(
          2,
          BookedDate(
              id: 'id',
              startDate: DateTime.now(),
              endDate: DateTime.now(),
              startTime: '2:00 PM'),
        ),
        category: 'SUV',
        year: '2024',
        carToPickupDistanceKm: 0,
        pickupToDestinationDistanceKm: 0,
        totalDistanceKm: 0);
  }

  Car copyWith({
    String? id,
    String? model,
    Schedule? schedule,
    num? dailyPrice,
    num? dailyMinPrice,
    int? ratingSummary,
    int? bookingCount,
    String? brand,
    num? totalEarned,
    bool? isAvailable,
    List<CarImage>? images,
    LuxAddress? address,
    int? doorCount,
    bool? hasAirCondition,
    num? acceleration,
    num? topSpeed,
    List<Review>? reviews,
    List<BookedDate>? bookedDates,
    String? category,
    String? year,
    num? carToPickupDistanceKm,
    num? pickupToDestinationDistanceKm,
    num? totalDistanceKm,
  }) {
    return Car(
      id: id ?? this.id,
      model: model ?? this.model,
      schedule: schedule ?? this.schedule,
      dailyPrice: dailyPrice ?? this.dailyPrice,
      dailyMinPrice: dailyMinPrice ?? this.dailyMinPrice,
      ratingSummary: ratingSummary ?? this.ratingSummary,
      bookingCount: bookingCount ?? this.bookingCount,
      brand: brand ?? this.brand,
      totalEarned: totalEarned ?? this.totalEarned,
      isAvailable: isAvailable ?? this.isAvailable,
      images: images ?? this.images,
      address: address ?? this.address,
      doorCount: doorCount ?? this.doorCount,
      hasAirCondition: hasAirCondition ?? this.hasAirCondition,
      acceleration: acceleration ?? this.acceleration,
      topSpeed: topSpeed ?? this.topSpeed,
      reviews: reviews ?? this.reviews,
      bookedDates: bookedDates ?? this.bookedDates,
      category: category ?? this.category,
      year: year ?? this.year,
      carToPickupDistanceKm:
          carToPickupDistanceKm ?? this.carToPickupDistanceKm,
      pickupToDestinationDistanceKm:
          pickupToDestinationDistanceKm ?? this.pickupToDestinationDistanceKm,
      totalDistanceKm: totalDistanceKm ?? this.totalDistanceKm,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'model': model,
      'schedule': schedule?.name,
      'dailyPrice': dailyPrice,
      'dailyMinPrice': dailyMinPrice,
      'ratingSummary': ratingSummary,
      'bookingCount': bookingCount,
      'brand': brand,
      'totalEarned': totalEarned,
      'isAvailable': isAvailable,
      'images': images.map((x) => x.toMap()).toList(),
      'address': address?.toMap(),
      'doorCount': doorCount,
      'hasAirCondition': hasAirCondition,
      'acceleration': acceleration,
      'topSpeed': topSpeed,
      'reviews': reviews.map((x) => x.toMap()).toList(),
      'bookedDates': bookedDates.map((x) => x.toMap()).toList(),
      'category': category,
      'year': year,
      'carToPickupDistanceKm': carToPickupDistanceKm,
      'pickupToDestinationDistanceKm': pickupToDestinationDistanceKm,
      'totalDistanceKm': totalDistanceKm,
    };
  }

  factory Car.fromMap(Map<String, dynamic> map) {
    return Car(
      id: map['_id'] ?? '',
      model: map['model'] ?? '',
      schedule:
          map['schedule'] != null ? Schedule.fromString(map['schedule']) : null,
      dailyPrice: map['dailyPrice'] ?? 0,
      dailyMinPrice: map['dailyMinPrice'] ?? 0,
      ratingSummary: map['ratingSummary']?.toInt() ?? 0,
      bookingCount: map['bookingCount']?.toInt() ?? 0,
      brand:
          (map['brand'] is String) ? map['brand'] : map['brand']?['name'] ?? '',
      totalEarned: map['totalEarned'] ?? 0,
      isAvailable: map['isAvailable'] ?? false,
      images: (map['carImages'] != null &&
              map['carImages'] is List &&
              map['carImages'].isNotEmpty
          ? List<CarImage>.from(
              map['carImages'].map((x) => CarImage.fromMap(x)))
          : []),
      address:
          map['address'] != null ? LuxAddress.fromMap(map['address']) : null,
      doorCount: map['doorCount']?.toInt() ?? 0,
      hasAirCondition: map['hasAirCondition'] ?? false,
      acceleration: map['acceleration'] ?? 0,
      topSpeed: map['topSpeed'] ?? 0,
      reviews: (map['reviews'] != null &&
              map['reviews'] is List &&
              map['reviews'].isNotEmpty)
          ? List<Review>.from(map['reviews']?.map((x) => Review.fromMap(x)))
          : [],
      bookedDates: (map['bookedDates'] != null &&
              map['bookedDates'] is List &&
              map['bookedDates'].isNotEmpty)
          ? List<BookedDate>.from(
              map['bookedDates']?.map((x) => BookedDate.fromMap(x)))
          : [],
      category: (map['category'] is String)
          ? map['category']
          : map['category']?['name'] ?? '',
      year: map['year'] ?? '',
      carToPickupDistanceKm: map['carToPickupDistanceKm'] ?? 0,
      pickupToDestinationDistanceKm: map['pickupToDestinationDistanceKm'] ?? 0,
      totalDistanceKm: map['totalDistanceKm'] ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory Car.fromJson(String source) => Car.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props {
    return [
      id,
      model,
      schedule,
      dailyPrice,
      dailyMinPrice,
      ratingSummary,
      bookingCount,
      brand,
      totalEarned,
      isAvailable,
      images,
      address,
      doorCount,
      hasAirCondition,
      acceleration,
      topSpeed,
      reviews,
      category,
      year,
      carToPickupDistanceKm,
      pickupToDestinationDistanceKm,
      totalDistanceKm,
    ];
  }

  /// returns the main image url
  String? get imageUrl => images.isNotEmpty
      ? images.firstWhereOrNull((e) => e.isMain)?.url ?? images.first.url
      : null;

  // Use dailyPrice instead of dailyMinPrice when either the distance from the car's location to the pickup point exceeds 20 km, or when the total trip distance (car location → pickup → destination) exceeds 100 km.
  num get price => totalDistanceKm > 100
      ? dailyPrice
      : carToPickupDistanceKm > 20
          ? dailyPrice
          : dailyMinPrice;

  String get name => '$brand $model';
  num get hourlyPrice => dailyPrice / 12;
  String? get passengerCount => null;
}

enum Schedule {
  dayNight('Day/Night'),
  dayOnly('Day Only'),
  nightOnly('Night Only');

  final String name;

  const Schedule(this.name);

  // Parse a string to a Schedule enum
  static Schedule fromString(String scheduleName) {
    return Schedule.values.firstWhere(
      (schedule) => schedule.name.toLowerCase() == scheduleName.toLowerCase(),
      orElse: () => throw ArgumentError('Invalid schedule name: $scheduleName'),
    );
  }

  @override
  String toString() {
    return name;
  }
}

class CarImage {
  final String id;
  final String carId;
  final String url;
  final bool isMain;
  final bool isVisible;
  final ResponsiveImageUrls? responsiveUrls;

  CarImage({
    required this.id,
    required this.carId,
    required this.url,
    required this.isMain,
    required this.isVisible,
    this.responsiveUrls,
  });

  factory CarImage.defaultImage() {
    return CarImage(
      id: randomString(12),
      carId: randomString(12),
      url: kImages.first,
      isMain: true,
      isVisible: true,
      responsiveUrls: ResponsiveImageUrls(
        thumbnail:
            'http://res.cloudinary.com/kadismile/image/upload/v1727126615/1727126610490-photo_images.jpg.jpg',
        medium:
            'http://res.cloudinary.com/kadismile/image/upload/v1727126615/1727126610490-photo_images.jpg.jpg',
        large:
            'http://res.cloudinary.com/kadismile/image/upload/v1727126615/1727126610490-photo_images.jpg.jpg',
      ),
    );
  }

  CarImage copyWith({
    bool? isMain,
    bool? isVisible,
  }) {
    return CarImage(
      id: id,
      carId: carId,
      url: url,
      isMain: isMain ?? this.isMain,
      isVisible: isVisible ?? this.isVisible,
      responsiveUrls: responsiveUrls,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'carId': carId,
      'url': url,
      'isMain': isMain,
      'isVisible': isVisible,
      'responsiveUrls': responsiveUrls?.toMap(),
    };
  }

  factory CarImage.fromMap(Map<String, dynamic> map) {
    return CarImage(
      id: map['_id'] ?? '',
      carId: map['carId'] ?? '',
      url: map['url'] ?? '',
      isMain: map['isMain'] ?? false,
      isVisible: map['isVisible'] ?? true,
      responsiveUrls: map['responsive_urls'] != null
          ? ResponsiveImageUrls.fromMap(map['responsive_urls'])
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory CarImage.fromJson(String source) =>
      CarImage.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}

class ResponsiveImageUrls {
  final String thumbnail;
  final String medium;
  final String large;

  ResponsiveImageUrls({
    required this.thumbnail,
    required this.medium,
    required this.large,
  });

  ResponsiveImageUrls copyWith({
    String? thumbnail,
    String? medium,
    String? large,
  }) {
    return ResponsiveImageUrls(
      thumbnail: thumbnail ?? this.thumbnail,
      medium: medium ?? this.medium,
      large: large ?? this.large,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'thumbnail': thumbnail,
      'medium': medium,
      'large': large,
    };
  }

  factory ResponsiveImageUrls.fromMap(Map<String, dynamic> map) {
    return ResponsiveImageUrls(
      thumbnail: map['thumbnail'] ?? '',
      medium: map['medium'] ?? '',
      large: map['large'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory ResponsiveImageUrls.fromJson(String source) =>
      ResponsiveImageUrls.fromMap(json.decode(source));

  @override
  String toString() =>
      'ResponsiveImageUrls(thumbnail: $thumbnail, medium: $medium, large: $large)';
}

class Brand {
  final String id;
  final String name;

  factory Brand.defaultBrand() {
    return Brand(id: '', name: '');
  }

  Brand({
    required this.id,
    required this.name,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory Brand.fromMap(Map<String, dynamic> map) {
    return Brand(
      id: map['_id'] ?? '',
      name: map['name'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Brand.fromJson(String source) => Brand.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}

class Category extends Brand {
  Category({
    required super.id,
    required super.name,
  });

  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['_id'] ?? '',
      name: map['name'] ?? '',
    );
  }

  factory Category.defaultCategory() {
    return Category(id: '', name: '');
  }
}

class BookedDate {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  final String startTime;
  BookedDate({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.startTime,
  });

  BookedDate copyWith({
    String? id,
    DateTime? startDate,
    DateTime? endDate,
    String? startTime,
  }) {
    return BookedDate(
      id: id ?? this.id,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      startTime: startTime ?? this.startTime,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      'startTime': startTime,
    };
  }

  factory BookedDate.fromMap(Map<String, dynamic> map) {
    return BookedDate(
      id: map['_id'] ?? '',
      startDate: parseDate(map['startDate'])!,
      endDate: parseDate(map['endDate'])!,
      startTime: map['startTime'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory BookedDate.fromJson(String source) =>
      BookedDate.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}

class Rating {
  Rating();

  Map<String, dynamic> toMap() {
    return {};
  }

  factory Rating.fromMap(Map<String, dynamic> map) {
    return Rating();
  }
}

// class Car extends Equatable {
//   final String id;
//   final String model;
//   final Schedule schedule;
//   final num dailyPrice;
//   final num dailyMinPrice;
//   final int ratingSummary;
//   final int bookingCount;
//   final Brand brand;
//   final Category category;
//   final num totalEarned;
//   final bool isAvailable;
//   final List<CarImage> images;
//   final LuxAddress address;

//   const Car({
//     required this.id,
//     required this.model,
//     required this.schedule,
//     required this.dailyPrice,
//     required this.dailyMinPrice,
//     required this.ratingSummary,
//     required this.bookingCount,
//     required this.brand,
//     required this.category,
//     required this.totalEarned,
//     required this.isAvailable,
//     required this.images,
//     required this.address,
//   });

//   factory Car.defaultCar() {
//     return Car(
//         id: randomString(10),
//         model: 'default_model',
//         schedule: Schedule.dayNight,
//         dailyPrice: 0,
//         dailyMinPrice: 0,
//         ratingSummary: 0,
//         bookingCount: 0,
//         brand: Brand.defaultBrand(),
//         category: Category.defaultCategory(),
//         totalEarned: 0,
//         isAvailable: false,
//         images: List.filled(5, CarImage.defaultImage()),
//         address: LuxAddress());
//   }

//   Car copyWith({
//     String? id,
//     String? model,
//     Schedule? schedule,
//     num? dailyPrice,
//     num? dailyMinPrice,
//     int? ratingSummary,
//     int? bookingCount,
//     Brand? brand,
//     Category? category,
//     num? totalEarned,
//     bool? isAvailable,
//     List<CarImage>? images,
//     LuxAddress? address,
//   }) {
//     return Car(
//       id: id ?? this.id,
//       model: model ?? this.model,
//       schedule: schedule ?? this.schedule,
//       dailyPrice: dailyPrice ?? this.dailyPrice,
//       dailyMinPrice: dailyMinPrice ?? this.dailyMinPrice,
//       ratingSummary: ratingSummary ?? this.ratingSummary,
//       bookingCount: bookingCount ?? this.bookingCount,
//       brand: brand ?? this.brand,
//       category: category ?? this.category,
//       totalEarned: totalEarned ?? this.totalEarned,
//       isAvailable: isAvailable ?? this.isAvailable,
//       images: images ?? this.images,
//       address: address ?? this.address,
//     );
//   }

//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'model': model,
//       'schedule': schedule.name,
//       'dailyPrice': dailyPrice,
//       'dailyMinPrice': dailyMinPrice,
//       'ratingSummary': ratingSummary,
//       'bookingCount': bookingCount,
//       'brand': brand.toMap(),
//       'category': category.toMap(),
//       'totalEarned': totalEarned,
//       'isAvailable': isAvailable,
//       'images': images.map((x) => x.toMap()).toList(),
//       'address': address.toMap(),
//     };
//   }

//   factory Car.fromMap(Map<String, dynamic> map) {
//     return Car(
//       id: map['_id'] ?? '',
//       model: map['model'] ?? '',
//       schedule: Schedule.fromString(map['schedule']),
//       dailyPrice: map['dailyPrice'] ?? 0,
//       dailyMinPrice: map['dailyMinPrice'] ?? 0,
//       ratingSummary: map['ratingSummary'] ?? 0,
//       bookingCount: map['bookingCount']?.toInt() ?? 0,
//       brand: Brand.fromMap(map['brand']),
//       category: Category.fromMap(map['category']),
//       totalEarned: map['totalEarned'] ?? 0,
//       isAvailable: map['isAvailable'] ?? false,
//       images: List<CarImage>.from(
//           map['carImages']?.map((x) => CarImage.fromMap(x))),
//       address: LuxAddress.fromMap(map['address']),
//     );
//   }

//   String toJson() => json.encode(toMap());

//   factory Car.fromJson(String source) => Car.fromMap(json.decode(source));

//   @override
//   String toString() => '${toMap()}';

//   @override
//   List<Object> get props {
//     return [
//       id,
//       model,
//       schedule,
//       dailyPrice,
//       dailyMinPrice,
//       ratingSummary,
//       bookingCount,
//       brand,
//       category,
//       totalEarned,
//       isAvailable,
//       images,
//       address,
//     ];
//   }

//   /// returns the main image url
//   String? get imageUrl => images.isNotEmpty
//       ? images.firstWhereOrNull((e) => e.isMain)?.url ?? images.first.url
//       : null;
//   String get name => '${brand.name} $model';
//   num get hourlyPrice => dailyPrice / 12;
// }

// enum Schedule {
//   dayNight('Day/Night'),
//   dayOnly('Day Only'),
//   nightOnly('Night Only');

//   final String name;

//   const Schedule(this.name);

//   // Parse a string to a Schedule enum
//   static Schedule fromString(String scheduleName) {
//     return Schedule.values.firstWhere(
//       (schedule) => schedule.name.toLowerCase() == scheduleName.toLowerCase(),
//       orElse: () => throw ArgumentError('Invalid schedule name: $scheduleName'),
//     );
//   }

//   @override
//   String toString() {
//     return name;
//   }
// }

// class CarImage {
//   final String id;
//   final String carId;
//   final String url;
//   final bool isMain;
//   final bool isVisible;

//   CarImage({
//     required this.id,
//     required this.carId,
//     required this.url,
//     required this.isMain,
//     required this.isVisible,
//   });

//   factory CarImage.defaultImage() {
//     return CarImage(
//       id: randomString(12),
//       carId: randomString(12),
//       url: kImages.first,
//       isMain: true,
//       isVisible: true,
//     );
//   }

//   CarImage copyWith({
//     bool? isMain,
//     bool? isVisible,
//   }) {
//     return CarImage(
//       id: id,
//       carId: carId,
//       url: url,
//       isMain: isMain ?? this.isMain,
//       isVisible: isVisible ?? this.isVisible,
//     );
//   }

//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'carId': carId,
//       'url': url,
//       'isMain': isMain,
//       'isVisible': isVisible,
//     };
//   }

//   factory CarImage.fromMap(Map<String, dynamic> map) {
//     return CarImage(
//       id: map['_id'] ?? '',
//       carId: map['carId'] ?? '',
//       url: map['url'] ?? '',
//       isMain: map['isMain'] ?? false,
//       isVisible: map['isVisible'] ?? true,
//     );
//   }

//   String toJson() => json.encode(toMap());

//   factory CarImage.fromJson(String source) =>
//       CarImage.fromMap(json.decode(source));

//   @override
//   String toString() => '${toMap()}';
// }

// class Brand {
//   final String id;
//   final String name;

//   factory Brand.defaultBrand() {
//     return Brand(id: '', name: '');
//   }

//   Brand({
//     required this.id,
//     required this.name,
//   });

//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'name': name,
//     };
//   }

//   factory Brand.fromMap(Map<String, dynamic> map) {
//     return Brand(
//       id: map['_id'] ?? '',
//       name: map['name'] ?? '',
//     );
//   }

//   String toJson() => json.encode(toMap());

//   factory Brand.fromJson(String source) => Brand.fromMap(json.decode(source));

//   @override
//   String toString() => '${toMap()}';
// }

// class Category extends Brand {
//   Category({
//     required super.id,
//     required super.name,
//   });

//   factory Category.fromMap(Map<String, dynamic> map) {
//     return Category(
//       id: map['_id'] ?? '',
//       name: map['name'] ?? '',
//     );
//   }

//   factory Category.defaultCategory() {
//     return Category(id: '', name: '');
//   }
// }
