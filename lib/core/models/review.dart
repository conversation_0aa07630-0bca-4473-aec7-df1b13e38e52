import 'package:fleet_mobile/core/core.dart';

class Review {
  final String id;
  final String userId;
  final String bookingId;
  final String comment;
  final int rating;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ReviewUser user;
  Review({
    required this.id,
    required this.userId,
    required this.bookingId,
    required this.comment,
    required this.rating,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
  });

  factory Review.defaultReview() {
    return Review(
      id: randomString(12),
      userId: randomString(12),
      bookingId: randomString(12),
      comment:
          'LuxLet made my dream of driving a luxury car come true. Their fleet is unparalleled...',
      rating: 5,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      user: ReviewUser.defaultUser(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'booking_id': bookingId,
      'comment': comment,
      'rating': rating,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'user': user.toMap(),
    };
  }

  factory Review.fromMap(Map<String, dynamic> map) {
    return Review(
      id: map['id'].toInt(),
      userId: map['user_id'].toInt(),
      bookingId: map['booking_id'].toInt(),
      comment: map['comment'],
      rating: int.parse(map['rating']),
      createdAt: parseDate(map['created_at'])!,
      updatedAt: parseDate(map['updated_at'])!,
      user: ReviewUser.fromMap(map['user']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Review &&
        other.id == id &&
        other.userId == userId &&
        other.bookingId == bookingId &&
        other.comment == comment &&
        other.rating == rating &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        bookingId.hashCode ^
        comment.hashCode ^
        rating.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}

class ReviewUser {
  final String id;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  ReviewUser({
    required this.id,
    this.firstName,
    this.lastName,
    this.phoneNumber,
  });

  factory ReviewUser.defaultUser() {
    return ReviewUser(
        id: randomString(12),
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '08011111111');
  }

  ReviewUser copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? phoneNumber,
  }) {
    return ReviewUser(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
    );
  }

  String get initials => _getInitials(firstName, lastName);
  String get fullName => _getFullName(firstName, lastName);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
    };
  }

  factory ReviewUser.fromMap(Map<String, dynamic> map) {
    return ReviewUser(
      id: map['id']?.toInt(),
      firstName: map['first_name'],
      lastName: map['last_name'],
      phoneNumber: map['phone_number'],
    );
  }

  @override
  String toString() => '${toMap()}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ReviewUser &&
        other.id == id &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.phoneNumber == phoneNumber;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        firstName.hashCode ^
        lastName.hashCode ^
        phoneNumber.hashCode;
  }
}

String _getInitials(String? firstName, String? lastName) {
  String initials = '';
  if (firstName != null) initials += firstName.substring(0, 1);
  if (lastName != null) initials += lastName.substring(0, 1);
  return initials.isNotEmpty ? initials : 'N/A';
}

String _getFullName(String? firstName, String? lastName) {
  String fullName = '';
  if (firstName != null) fullName += '$firstName ';
  if (lastName != null) fullName += lastName;
  return fullName.isNotEmpty ? fullName : 'N/A';
}
