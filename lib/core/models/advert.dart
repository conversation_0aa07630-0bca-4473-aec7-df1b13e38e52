// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:fleet_mobile/core/core.dart';

class Advert extends Equatable {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  // final bool isActive;
  final bool isApproved;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Car car;

  const Advert({
    required this.id,
    required this.startDate,
    required this.endDate,
    // required this.isActive,
    required this.isApproved,
    required this.createdAt,
    required this.updatedAt,
    required this.car,
  });

  String get imageUrl => car.imageUrl ?? '';
  String get carId => car.id;
  String get carName => car.name;
  String get carBrand => car.brand;
  String get carModel => car.model;
  String get carYear => car.year;

  factory Advert.fromMap(Map<String, dynamic> map) {
    return Advert(
      id: map['_id'] as String,
      startDate: parseDate(map['startDate'])!,
      endDate: parseDate(map['endDate'])!,
      // isActive: map['isActive'] as bool,
      isApproved: map['isApproved'] as bool,
      createdAt: parseDate(map['createdAt'])!,
      updatedAt: parseDate(map['updatedAt'])!,
      car: Car.fromMap(map['car'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      '_id': id,
      'startDate': startDate.millisecondsSinceEpoch,
      'endDate': endDate.millisecondsSinceEpoch,
      // 'isActive': isActive,
      'isApproved': isApproved,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'car': car.toMap(),
    };
  }

  Advert copyWith({
    String? id,
    DateTime? startDate,
    DateTime? endDate,
    // bool? isActive,
    bool? isApproved,
    DateTime? createdAt,
    DateTime? updatedAt,
    Car? car,
  }) {
    return Advert(
      id: id ?? this.id,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      // isActive: isActive ?? this.isActive,
      isApproved: isApproved ?? this.isApproved,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      car: car ?? this.car,
    );
  }

  String toJson() => json.encode(toMap());

  factory Advert.fromJson(String source) =>
      Advert.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props => [
        id,
        startDate,
        endDate,
        // isActive,
        isApproved,
        createdAt,
        updatedAt,
      ];

  factory Advert.defaultValue() => Advert(
        id: 'default-id',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 7)),
        // isActive: true,
        isApproved: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        car: Car.defaultValue(),
      );
}
