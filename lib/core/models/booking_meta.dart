class BookingMeta {
  final int totalBookings;
  final int totalBookingsPending;
  final int totalBookingsPendingPayment;
  final int totalBookingsPendingApproval;
  final int totalBookingsPaid;
  final int totalBookingsApproved;
  final int totalBookingsDeclined;
  final int totalBookingsCancelled;
  final int totalBookingsCompleted;
  final int totalBookingsInProgress;

  BookingMeta({
    required this.totalBookings,
    required this.totalBookingsPending,
    required this.totalBookingsPendingPayment,
    required this.totalBookingsPendingApproval,
    required this.totalBookingsPaid,
    required this.totalBookingsApproved,
    required this.totalBookingsDeclined,
    required this.totalBookingsCancelled,
    required this.totalBookingsCompleted,
    required this.totalBookingsInProgress,
  });

  factory BookingMeta.fromMap(Map<String, dynamic> map) {
    return BookingMeta(
      totalBookings: map['totalBookings']?.toInt() ?? 0,
      totalBookingsPending: map['totalBookingsPending']?.toInt() ?? 0,
      totalBookingsPendingPayment: map['totalBookingsPendingPayment']?.toInt() ?? 0,
      totalBookingsPendingApproval: map['totalBookingsPendingApproval']?.toInt() ?? 0,
      totalBookingsPaid: map['totalBookingsPaid']?.toInt() ?? 0,
      totalBookingsApproved: map['totalBookingsApproved']?.toInt() ?? 0,
      totalBookingsDeclined: map['totalBookingsDeclined']?.toInt() ?? 0,
      totalBookingsCancelled: map['totalBookingsCancelled']?.toInt() ?? 0,
      totalBookingsCompleted: map['totalBookingsCompleted']?.toInt() ?? 0,
      totalBookingsInProgress: map['totalBookingsInProgress']?.toInt() ?? 0,
    );
  }

  factory BookingMeta.defaultValue() {
    return BookingMeta(
      totalBookings: 0,
      totalBookingsPending: 0,
      totalBookingsPendingPayment: 0,
      totalBookingsPendingApproval: 0,
      totalBookingsPaid: 0,
      totalBookingsApproved: 0,
      totalBookingsDeclined: 0,
      totalBookingsCancelled: 0,
      totalBookingsCompleted: 0,
      totalBookingsInProgress: 0,
    );
  }
}
