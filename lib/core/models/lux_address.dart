import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart';

class LuxAddress {
  final String? address;
  final String? city;
  final String? lga;
  final String? state;
  final String? countryCode;
  final double? latitude;
  final double? longitude;
  final String? postal;
  final String? area;
  final String? neighborhood;
  final String? country;

  LuxAddress({
    this.address,
    this.city,
    this.lga,
    this.state,
    this.countryCode,
    this.latitude,
    this.longitude,
    this.postal,
    this.area,
    this.neighborhood,
    this.country,
  });

  factory LuxAddress.defaultAddress() {
    return LuxAddress(
      address: 'Address',
      city: 'Lagos',
      lga: 'Amuwo Odofin',
      state: 'Lagos',
      countryCode: 'NG',
      latitude: 0.1234,
      longitude: 0.1234,
      postal: 'Postal',
      area: 'area',
      neighborhood: 'Festac',
      country: 'Nigeria',
    );
  }

  factory LuxAddress.fromPlace(Place place) {
    final data = <String, String>{};

    // Process address components from the new SDK
    if (place.addressComponents != null) {
      for (var component in place.addressComponents!) {
        // Check for administrative area level 1 (state)
        if (component.types.any((type) =>
            type.toString().contains('administrative_area_level_1'))) {
          data['state'] = component.name;
          continue;
        }

        // Check for administrative area level 2 (LGA)
        if (component.types.any((type) =>
            type.toString().contains('administrative_area_level_2'))) {
          data['lga'] = component.name;
          continue;
        }

        // Check for locality (city)
        if (component.types
            .any((type) => type.toString().contains('locality'))) {
          data['city'] = component.name;
          continue;
        }

        // Check for sublocality level 1 or administrative_area_level_3 (area)
        if (component.types
            .any((type) => type.toString().contains('sublocality_level_1'))) {
          data['area'] = component.name;
          continue;
        } else if (component.types.any((type) =>
            type.toString().contains('administrative_area_level_3'))) {
          data['area'] = component.name;
          continue;
        }

        // Check for neighborhood
        if (component.types
            .any((type) => type.toString().contains('neighborhood'))) {
          data['neighborhood'] = component.name;
          continue;
        }

        // Check for country
        if (component.types
            .any((type) => type.toString().contains('country'))) {
          data['countryCode'] = component.shortName;
          data['country'] = component.name;
          continue;
        }

        // Check for postal code
        if (component.types
            .any((type) => type.toString().contains('postal_code'))) {
          data['postal'] = component.name;
        }
      }
    }

    return LuxAddress(
      address: place.name,
      city: data['city'] ?? data['lga'] ?? data['state'], // fallback priority
      lga: data['lga'] ?? data['area'], // fallback priority,
      state: data['state'],
      countryCode: data['countryCode'],
      country: data['country'],
      postal: data['postal'],
      latitude: place.latLng?.lat,
      longitude: place.latLng?.lng,
      area: data['area'],
      neighborhood: data['neighborhood'],
    );
  }

  // factory LuxAddress.fromPlaceDetail(PlaceDetails detail) {
  //   final data = <String, String>{};

  //   for (var element in detail.addressComponents) {
  //     if (element.types.contains('administrative_area_level_1')) {
  //       data['city'] = element.longName;
  //       continue;
  //     }

  //     if (element.types.contains('administrative_area_level_2')) {
  //       data['lga'] = element.longName;
  //       continue;
  //     }

  //     if (element.types.contains('administrative_area_level_3')) {
  //       data['area'] = element.longName;
  //     }

  //     if (element.types.contains('neighborhood')) {
  //       data['neighborhood'] = element.longName;
  //       continue;
  //     }

  //     if (element.types.contains('locality')) {
  //       data['state'] = element.longName;
  //       continue;
  //     }

  //     if (element.types.contains('country')) {
  //       data['countryCode'] = element.shortName;
  //       data['country'] = element.longName;
  //       continue;
  //     }

  //     if (element.types.contains('postal_code')) {
  //       data['postal'] = element.longName;
  //       continue;
  //     }
  //   }

  //   final extractedState = data['state'] ?? data['countryCode'];

  //   return LuxAddress(
  //     address: detail.formattedAddress,
  //     city: data['city'] ?? extractedState,
  //     state: data['state'] ?? extractedState,
  //     lga: data['lga'] ?? extractedState,
  //     countryCode: data['countryCode'],
  //     country: data['country'],
  //     postal: data['postal'],
  //     latitude: detail.geometry?.location.lat,
  //     longitude: detail.geometry?.location.lng,
  //     area: data['area'],
  //     neighborhood: data['neighborhood'],
  //   );
  // }

  factory LuxAddress.fromMap(Map<String, dynamic> map) {
    return LuxAddress(
      address: map['fullAddress'],
      city: map['city'],
      lga: map['lga'],
      state: map['state'],
      countryCode: map['countryCode'],
      country: map['country'],
      postal: map['postal'],
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      area: map['area'],
      neighborhood: map['neighborhood'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fullAddress': address ?? '',
      'city': city ?? '',
      'lga': lga ?? area ?? '',
      'state': state ?? '',
      'countryCode': countryCode ?? '',
      'country': country ?? '',
      // 'postal': postal,
      'latitude': latitude,
      'longitude': longitude,
      // 'area': area,
      if (neighborhood != null) 'neighborhood': neighborhood,
    };
  }

  bool get hasNullValues => [
        address,
        city,
        lga,
        state,
        countryCode,
        country,
        postal,
        latitude,
        longitude,
        area,
        neighborhood,
      ].any((field) => field == null);
}

typedef SetAddress = void Function(LuxAddress address);

// // ignore: depend_on_referenced_packages
// import 'package:google_maps_webservice/places.dart';

// class LuxAddress {
//   final String? address;
//   final String? city;
//   final String? lga;
//   final String? state;
//   final String? countryCode;
//   final double? latitude;
//   final double? longitude;
//   final String? postal;
//   final String? area;
//   final String? neighborhood;
//   final String? country;

//   LuxAddress({
//     this.address,
//     this.city,
//     this.lga,
//     this.state,
//     this.countryCode,
//     this.latitude,
//     this.longitude,
//     this.postal,
//     this.area,
//     this.neighborhood,
//     this.country,
//   });

//   factory LuxAddress.defaultAddress() {
//     return LuxAddress(
//       address: 'Address',
//       city: 'Lagos',
//       lga: 'Amuwo Odofin',
//       state: 'Lagos',
//       countryCode: 'NG',
//       latitude: 0.1234,
//       longitude: 0.1234,
//       postal: 'Postal',
//       area: 'area',
//       neighborhood: 'Festac',
//       country: 'Nigeria',
//     );
//   }

//   factory LuxAddress.fromPlaceDetail(PlaceDetails detail) {
//     final data = <String, String>{};

//     for (var component in detail.addressComponents) {
//       if (component.types.contains('administrative_area_level_1')) {
//         data['state'] = component.longName;
//         continue;
//       }

//       if (component.types.contains('administrative_area_level_2')) {
//         data['lga'] = component.longName;
//         continue;
//       }

//       if (component.types.contains('locality')) {
//         data['city'] = component.longName;
//         continue;
//       }

//       if (component.types.contains('sublocality_level_1')) {
//         data['area'] = component.longName;
//         continue;
//       }

//       if (component.types.contains('neighborhood')) {
//         data['neighborhood'] = component.longName;
//         continue;
//       }

//       if (component.types.contains('country')) {
//         data['countryCode'] = component.shortName;
//         data['country'] = component.longName;
//         continue;
//       }

//       if (component.types.contains('postal_code')) {
//         data['postal'] = component.longName;
//       }
//     }

//     return LuxAddress(
//       address: detail.formattedAddress,
//       city: data['city'] ?? data['lga'] ?? data['state'], // fallback priority
//       lga: data['lga'],
//       state: data['state'],
//       countryCode: data['countryCode'],
//       country: data['country'],
//       postal: data['postal'],
//       latitude: detail.geometry?.location.lat,
//       longitude: detail.geometry?.location.lng,
//       area: data['area'],
//       neighborhood: data['neighborhood'],
//     );
//   }

//   // factory LuxAddress.fromPlaceDetail(PlaceDetails detail) {
//   //   final data = <String, String>{};

//   //   for (var element in detail.addressComponents) {
//   //     if (element.types.contains('administrative_area_level_1')) {
//   //       data['city'] = element.longName;
//   //       continue;
//   //     }

//   //     if (element.types.contains('administrative_area_level_2')) {
//   //       data['lga'] = element.longName;
//   //       continue;
//   //     }

//   //     if (element.types.contains('administrative_area_level_3')) {
//   //       data['area'] = element.longName;
//   //     }

//   //     if (element.types.contains('neighborhood')) {
//   //       data['neighborhood'] = element.longName;
//   //       continue;
//   //     }

//   //     if (element.types.contains('locality')) {
//   //       data['state'] = element.longName;
//   //       continue;
//   //     }

//   //     if (element.types.contains('country')) {
//   //       data['countryCode'] = element.shortName;
//   //       data['country'] = element.longName;
//   //       continue;
//   //     }

//   //     if (element.types.contains('postal_code')) {
//   //       data['postal'] = element.longName;
//   //       continue;
//   //     }
//   //   }

//   //   final extractedState = data['state'] ?? data['countryCode'];

//   //   return LuxAddress(
//   //     address: detail.formattedAddress,
//   //     city: data['city'] ?? extractedState,
//   //     state: data['state'] ?? extractedState,
//   //     lga: data['lga'] ?? extractedState,
//   //     countryCode: data['countryCode'],
//   //     country: data['country'],
//   //     postal: data['postal'],
//   //     latitude: detail.geometry?.location.lat,
//   //     longitude: detail.geometry?.location.lng,
//   //     area: data['area'],
//   //     neighborhood: data['neighborhood'],
//   //   );
//   // }

//   factory LuxAddress.fromMap(Map<String, dynamic> map) {
//     return LuxAddress(
//       address: map['fullAddress'],
//       city: map['city'],
//       lga: map['lga'],
//       state: map['state'],
//       countryCode: map['countryCode'],
//       country: map['country'],
//       postal: map['postal'],
//       latitude: map['latitude']?.toDouble(),
//       longitude: map['longitude']?.toDouble(),
//       area: map['area'],
//       neighborhood: map['neighborhood'],
//     );
//   }

//   Map<String, dynamic> toMap() {
//     return {
//       'fullAddress': address,
//       'city': city,
//       'lga': lga,
//       'state': state,
//       'countryCode': countryCode,
//       'country': country,
//       // 'postal': postal,
//       'latitude': latitude,
//       'longitude': longitude,
//       // 'area': area,
//       if (neighborhood != null) 'neighborhood': neighborhood,
//     };
//   }

//   bool get hasNullValues => [
//         address,
//         city,
//         lga,
//         state,
//         countryCode,
//         country,
//         postal,
//         latitude,
//         longitude,
//         area,
//         neighborhood,
//       ].any((field) => field == null);
// }

// typedef SetAddress = void Function(LuxAddress address);
