import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:fleet_mobile/core/core.dart';

class Wallet extends Equatable {
  final num availableBalance;
  final num grossEarnings;
  final num netEarnings;
  final num totalWithdrawn;
  final num totalPlatformFees;
  final num totalRefunds;
  final num pendingEarnings;
  final DateTime lastReconciledAt;
  const Wallet({
    required this.availableBalance,
    required this.grossEarnings,
    required this.netEarnings,
    required this.totalWithdrawn,
    required this.totalPlatformFees,
    required this.totalRefunds,
    required this.pendingEarnings,
    required this.lastReconciledAt,
  });

  Wallet copyWith({
    num? availableBalance,
    num? grossEarnings,
    num? netEarnings,
    num? totalWithdrawn,
    num? totalPlatformFees,
    num? totalRefunds,
    num? pendingEarnings,
    DateTime? lastReconciledAt,
  }) {
    return Wallet(
      availableBalance: availableBalance ?? this.availableBalance,
      grossEarnings: grossEarnings ?? this.grossEarnings,
      netEarnings: netEarnings ?? this.netEarnings,
      totalWithdrawn: totalWithdrawn ?? this.totalWithdrawn,
      totalPlatformFees: totalPlatformFees ?? this.totalPlatformFees,
      totalRefunds: totalRefunds ?? this.totalRefunds,
      pendingEarnings: pendingEarnings ?? this.pendingEarnings,
      lastReconciledAt: lastReconciledAt ?? this.lastReconciledAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'availableBalance': availableBalance,
      'grossEarnings': grossEarnings,
      'netEarnings': netEarnings,
      'totalWithdrawn': totalWithdrawn,
      'totalPlatformFees': totalPlatformFees,
      'totalRefunds': totalRefunds,
      'pendingEarnings': pendingEarnings,
      'lastReconciledAt': lastReconciledAt.millisecondsSinceEpoch,
    };
  }

  factory Wallet.fromMap(Map<String, dynamic> map) {
    return Wallet(
      availableBalance: map['availableBalance'] ?? 0,
      grossEarnings: map['grossEarnings'] ?? 0,
      netEarnings: map['netEarnings'] ?? 0,
      totalWithdrawn: map['totalWithdrawn'] ?? 0,
      totalPlatformFees: map['totalPlatformFees'] ?? 0,
      totalRefunds: map['totalRefunds'] ?? 0,
      pendingEarnings: map['pendingEarnings'] ?? 0,
      lastReconciledAt: parseDate(map['lastReconciledAt'])!,
    );
  }

  factory Wallet.defaultValue() => Wallet(
        availableBalance: 0,
        grossEarnings: 0,
        netEarnings: 0,
        totalWithdrawn: 0,
        totalPlatformFees: 0,
        totalRefunds: 0,
        pendingEarnings: 0,
        lastReconciledAt: DateTime.now(),
      );

  String toJson() => json.encode(toMap());

  factory Wallet.fromJson(String source) => Wallet.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Wallet(availableBalance: $availableBalance, grossEarnings: $grossEarnings, netEarnings: $netEarnings, totalWithdrawn: $totalWithdrawn, totalPlatformFees: $totalPlatformFees, totalRefunds: $totalRefunds, pendingEarnings: $pendingEarnings, lastReconciledAt: $lastReconciledAt)';
  }

  @override
  List<Object> get props {
    return [
      availableBalance,
      grossEarnings,
      netEarnings,
      totalWithdrawn,
      totalPlatformFees,
      totalRefunds,
      pendingEarnings,
      lastReconciledAt,
    ];
  }
}
