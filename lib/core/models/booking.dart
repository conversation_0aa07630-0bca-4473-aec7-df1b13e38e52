import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:fleet_mobile/core/core.dart';

class Booking extends Equatable {
  final String id;
  final String clientId;
  final String companyId;
  final String managerId;
  final Car car;
  final DateTime startDate;
  final String startTime;
  final DateTime endDate;
  final String status;
  final String approvalStatus;
  final String bookingType;
  final LuxAddress pickupAddress;
  final LuxAddress destinationAddress;
  final int escortCount;
  final int escortDays;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int numberOfDays;
  final double pickupKilometer;
  final double destinationKilometer;
  final double totalPrice;
  const Booking({
    required this.id,
    required this.clientId,
    required this.companyId,
    required this.managerId,
    required this.car,
    required this.startDate,
    required this.startTime,
    required this.endDate,
    required this.status,
    required this.approvalStatus,
    required this.bookingType,
    required this.pickupAddress,
    required this.destinationAddress,
    required this.escortCount,
    required this.escortDays,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.numberOfDays,
    required this.pickupKilometer,
    required this.destinationKilometer,
    required this.totalPrice,
  });

  factory Booking.defaultValue() {
    return Booking(
      id: 'default_id',
      clientId: 'default_client_id',
      companyId: 'default_company_id',
      managerId: 'default_manager_id',
      car: Car.defaultValue(),
      startDate: DateTime.now(),
      startTime: '00:00',
      endDate: DateTime.now().add(const Duration(days: 1)),
      status: 'pending',
      approvalStatus: 'pending',
      bookingType: 'standard',
      pickupAddress: LuxAddress.defaultAddress(),
      destinationAddress: LuxAddress.defaultAddress(),
      escortCount: 0,
      escortDays: 0,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      numberOfDays: 1,
      pickupKilometer: 0.0,
      destinationKilometer: 0.0,
      totalPrice: 0.0,
    );
  }

  Booking copyWith({
    String? companyId,
    String? managerId,
    Car? car,
    DateTime? startDate,
    String? startTime,
    DateTime? endDate,
    String? status,
    String? approvalStatus,
    String? bookingType,
    LuxAddress? pickupAddress,
    LuxAddress? destinationAddress,
    int? escortCount,
    int? escortDays,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? numberOfDays,
    double? pickupKilometer,
    double? destinationKilometer,
    double? totalPrice,
  }) {
    return Booking(
      id: id,
      clientId: clientId,
      companyId: companyId ?? this.companyId,
      managerId: managerId ?? this.managerId,
      car: car ?? this.car,
      startDate: startDate ?? this.startDate,
      startTime: startTime ?? this.startTime,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      bookingType: bookingType ?? this.bookingType,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      destinationAddress: destinationAddress ?? this.destinationAddress,
      escortCount: escortCount ?? this.escortCount,
      escortDays: escortDays ?? this.escortDays,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      numberOfDays: numberOfDays ?? this.numberOfDays,
      pickupKilometer: pickupKilometer ?? this.pickupKilometer,
      destinationKilometer: destinationKilometer ?? this.destinationKilometer,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'clientId': clientId,
      'companyId': companyId,
      'managerId': managerId,
      'car': car.toMap(),
      'startDate': startDate.toIso8601String(),
      'startTime': startTime,
      'endDate': endDate.toIso8601String(),
      'status': status,
      'approvalStatus': approvalStatus,
      'bookingType': bookingType,
      'pickupAddress': pickupAddress.toMap(),
      'destinationAddress': destinationAddress.toMap(),
      'escortCount': escortCount,
      'escortDays': escortDays,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'numberOfDays': numberOfDays,
      'pickupKilometer': pickupKilometer,
      'destinationKilometer': destinationKilometer,
      'totalPrice': totalPrice,
    };
  }

  factory Booking.fromMap(Map<String, dynamic> map) {
    return Booking(
      id: map['_id'],
      clientId: map['clientId'],
      companyId: map['companyId'] ?? '',
      managerId: map['managerId'] ?? '',
      car: Car.fromMap(map['car']),
      startDate: parseDate(map['startDate'])!,
      startTime: map['startTime'] ?? '',
      endDate: parseDate(map['endDate'])!,
      status: map['status'] ?? '',
      approvalStatus: map['approvalStatus'] ?? '',
      bookingType: map['bookingType'] ?? '',
      pickupAddress: LuxAddress.fromMap(map['pickupAddress']),
      destinationAddress: LuxAddress.fromMap(map['destinationAddress']),
      escortCount: map['escortCount']?.toInt() ?? 0,
      escortDays: map['escortDays']?.toInt() ?? 0,
      isActive: map['isActive'] ?? false,
      createdAt: parseDate(map['createdAt'])!,
      updatedAt: parseDate(map['updatedAt'])!,
      numberOfDays: map['numberOfDays']?.toInt() ?? 0,
      pickupKilometer: map['pickupKilometer']?.toDouble() ?? 0,
      destinationKilometer: map['destinationKilometer']?.toDouble() ?? 0.0,
      totalPrice: map['totalPrice']?.toDouble() ?? 0.0,
    );
  }

  String toJson() => json.encode(toMap());

  factory Booking.fromJson(String source) =>
      Booking.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';

  // Car get car => cars.first;
  int get carCount => 1;

  /// Checks if the booking was created within the last five minutes
  bool get isRecentlyCreated =>
      DateTime.now().difference(createdAt).inMinutes <= 5;

  /// Determines if the booking status is in an approved state
  bool get isApproved =>
      bookingApprovalApproved.contains(approvalStatus.toLowerCase());

  /// Determines if the booking has been paid for
  bool get isPaid => bookingStatusPaid.contains(status.toLowerCase());

  /// Checks if the booking is pending approval or processing
  bool get isPending =>
      bookingStatusPending.contains(approvalStatus.toLowerCase());

  /// Checks if the booking was canceled
  bool get isCanceled => bookingStatusCanceled.contains(status.toLowerCase());

  /// Determines if the booking is both recently created and approved, indicating it's on the way
  bool get isOnTheWay =>
      isRecentlyCreated &&
      isApproved; //TODO: Use the pickup date to determine when to show that a car is on the way. This approach would be better than what we currently have.

  double get paymentAmount => totalPrice * 100; // Using paystack for payment

  /// Checks if the booking was created today
  bool get isCreatedToday {
    final now = DateTime.now();
    return createdAt.year == now.year &&
        createdAt.month == now.month &&
        createdAt.day == now.day;
  }

  bool get hasApproval =>
      approvalStatus.toLowerCase() == 'approved' ||
      approvalStatus.toLowerCase() == 'declined';

  bool get isDeclined => approvalStatus.toLowerCase() == 'declined';
  bool get isAccepted => approvalStatus.toLowerCase() == 'approved';

// pending: When we initially create a booking.
// decline: When the manager declines the booking - we need to have a refund flow for when this happens.
// booked: I guess this is after payment is made.
// approved: When the manager approves the booking.
// canceled: When the client cancels the booking
// inProgress: When the booking trip starts.
// completed: When the booking trip has ended.

  @override
  List<Object?> get props => [
        id,
        clientId,
        companyId,
        managerId,
        car,
        startDate,
        startTime,
        endDate,
        status,
        bookingType,
        pickupAddress,
        destinationAddress,
        escortCount,
        escortDays,
        isActive,
        createdAt,
        updatedAt,
        numberOfDays,
        pickupKilometer,
        destinationKilometer,
        totalPrice,
      ];
}

// class Booking {
//   final int id;
//   final int userId;
//   final num totalPrice;
//   final num? amountPaid;
//   final bool? isInstant;
//   final bool? isDropOff;
//   final bool? isDeal;
//   final bool? isCompleted;
//   final String? scheduleStart;
//   final String? scheduleEnd;
//   final String? pickupTime;
//   final int? carManagerId;
//   final int? dealManagerId;
//   final int? statusId;
//   final int? policeEscortCount;
//   final String? pickupAddress;
//   final int? destinationAreaId;
//   final String? destinationAddress;
//   final DateTime createdAt;
//   final DateTime updatedAt;
//   final BookingType? bookingType;
//   final dynamic bookingTypeReadable;
//   final int? destinationType;
//   final CarManager? carManager;
//   final List<Car> cars;
//   // final Status? status;
//   final String status;
//   Booking({
//     required this.id,
//     required this.userId,
//     required this.totalPrice,
//     this.amountPaid,
//     this.isInstant,
//     this.isDropOff,
//     this.isDeal,
//     this.isCompleted,
//     this.scheduleStart,
//     this.scheduleEnd,
//     this.pickupTime,
//     this.carManagerId,
//     this.dealManagerId,
//     this.statusId,
//     this.policeEscortCount,
//     this.pickupAddress,
//     this.destinationAreaId,
//     this.destinationAddress,
//     required this.createdAt,
//     required this.updatedAt,
//     this.bookingTypeReadable,
//     this.bookingType,
//     this.destinationType,
//     this.carManager,
//     required this.cars,
//     // this.status,
//     required this.status,
//   });

//   Booking copyWith({
//     int? id,
//     int? userId,
//     num? totalPrice,
//     num? amountPaid,
//     bool? isInstant,
//     bool? isDeal,
//     bool? isDropOff,
//     bool? isCompleted,
//     String? scheduleStart,
//     String? scheduleEnd,
//     String? pickupTime,
//     int? carManagerId,
//     int? dealManagerId,
//     int? statusId,
//     int? policeEscortCount,
//     String? pickupAddress,
//     int? destinationAreaId,
//     String? destinationAddress,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//     BookingType? bookingType,
//     String? bookingTypeReadable,
//     int? destinationType,
//     CarManager? carManager,
//     List<Car>? cars,
//     // Status? status,
//     String? status,
//   }) {
//     return Booking(
//       id: id ?? this.id,
//       userId: userId ?? this.userId,
//       totalPrice: totalPrice ?? this.totalPrice,
//       amountPaid: amountPaid ?? this.amountPaid,
//       isInstant: isInstant ?? this.isInstant,
//       isDropOff: isDropOff ?? this.isDropOff,
//       isDeal: isDeal ?? this.isDeal,
//       isCompleted: isCompleted ?? this.isCompleted,
//       scheduleStart: scheduleStart ?? this.scheduleStart,
//       scheduleEnd: scheduleEnd ?? this.scheduleEnd,
//       pickupTime: pickupTime ?? this.pickupTime,
//       carManagerId: carManagerId ?? this.carManagerId,
//       dealManagerId: dealManagerId ?? this.dealManagerId,
//       statusId: statusId ?? this.statusId,
//       policeEscortCount: policeEscortCount ?? this.policeEscortCount,
//       pickupAddress: pickupAddress ?? this.pickupAddress,
//       destinationAreaId: destinationAreaId ?? this.destinationAreaId,
//       destinationAddress: destinationAddress ?? this.destinationAddress,
//       createdAt: createdAt ?? this.createdAt,
//       updatedAt: updatedAt ?? this.updatedAt,
//       bookingType: bookingType ?? this.bookingType,
//       bookingTypeReadable: bookingTypeReadable ?? this.bookingTypeReadable,
//       destinationType: destinationType ?? this.destinationType,
//       carManager: carManager ?? this.carManager,
//       cars: cars ?? this.cars,
//       // status: status ?? this.status,
//       status: status ?? this.status,
//     );
//   }

//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'user_id': userId,
//       'total_price': totalPrice,
//       'amount_paid': amountPaid,
//       'is_instant': isInstant,
//       'is_drop_off': isDropOff,
//       'is_deal': isDeal,
//       'is_completed': isCompleted,
//       'schedule_start': scheduleStart,
//       'schedule_end': scheduleEnd,
//       'pickup_time': pickupTime,
//       'car_manager_id': carManagerId,
//       'deal_manage_id': dealManagerId,
//       'status_id': statusId,
//       'police_escort_count': policeEscortCount,
//       'pickup_address': pickupAddress,
//       'destination_area_id': destinationAreaId,
//       'destination_address': destinationAddress,
//       'created_at': createdAt.millisecondsSinceEpoch,
//       'updated_at': updatedAt.millisecondsSinceEpoch,
//       'booking_type_readable': bookingTypeReadable,
//       'booking_type': bookingType?.name,
//       'destination_type': destinationType,
//       'carManager': carManager?.toMap(),
//       'cars': cars.map((x) => x.toMap()).toList(),
//       // 'status': status?.toMap(),
//       'status': status,
//     };
//   }

//   factory Booking.fromMap(Map<String, dynamic> map) {
//     return Booking(
//       id: map['id']?.toInt(),
//       userId: map['user_id']?.toInt(),
//       totalPrice: toNumber(map['total_price']) ?? 0,
//       amountPaid: toNumber(map['amount_paid']) ?? 0,
//       isInstant: map['is_instant'] ?? false,
//       isDropOff: BookingStyle.fromValue(map['is_drop_off']).toBool(),
//       isDeal: map['is_deal'] ?? false,
//       isCompleted: map['is_completed'] ?? false,
//       scheduleStart: map['schedule_start'],
//       scheduleEnd: map['schedule_end'],
//       pickupTime: map['pickup_time'],
//       carManagerId: map['car_manager_id']?.toInt(),
//       dealManagerId: map['deal_manager_id']?.toInt(),
//       statusId: map['status_id'],
//       policeEscortCount: map['police_escort_count']?.toInt(),
//       pickupAddress: map['pickup_address'] ?? '',
//       destinationAreaId: map['destination_area_id']?.toInt(),
//       destinationAddress: map['destination_address'],
//       createdAt: parseDate(map['created_at'])!,
//       updatedAt: parseDate(map['updated_at'])!,
//       bookingType: BookingType.fromValue(map['booking_type']),
//       bookingTypeReadable: map['booking_type_readable'],
//       destinationType: map['destination_type'],
//       carManager: map['carManager'] != null
//           ? CarManager.fromMap(map['carManager'])
//           : null,
//       cars: map['cars'] != null
//           ? List<Car>.from(map['cars']?.map((x) => Car.fromMap(x)))
//           : [],
//       // status: map['status'] != null ? Status.fromMap(map['status']) : null,
//       status: map['status_readable'] ?? 'Unknown',
//     );
//   }

//   String toJson() => json.encode(toMap());

//   factory Booking.fromJson(String source) =>
//       Booking.fromMap(json.decode(source));

//   @override
//   String toString() => '${toMap()}';

//   @override
//   bool operator ==(Object other) {
//     if (identical(this, other)) return true;

//     return other is Booking &&
//         other.id == id &&
//         other.userId == userId &&
//         other.totalPrice == totalPrice &&
//         other.amountPaid == amountPaid &&
//         other.isInstant == isInstant &&
//         other.isDropOff == isDropOff &&
//         other.isDeal == isDeal &&
//         other.isCompleted == isCompleted &&
//         other.scheduleStart == scheduleStart &&
//         other.scheduleEnd == scheduleEnd &&
//         other.pickupTime == pickupTime &&
//         other.carManagerId == carManagerId &&
//         other.dealManagerId == dealManagerId &&
//         other.statusId == statusId &&
//         other.policeEscortCount == policeEscortCount &&
//         other.pickupAddress == pickupAddress &&
//         other.destinationAreaId == destinationAreaId &&
//         other.destinationAddress == destinationAddress &&
//         other.createdAt == createdAt &&
//         other.updatedAt == updatedAt &&
//         other.bookingType == bookingType &&
//         other.bookingTypeReadable == bookingTypeReadable &&
//         other.destinationType == destinationType &&
//         other.carManager == carManager &&
//         listEquals(other.cars, cars) &&
//         other.status == status;
//   }

//   @override
//   int get hashCode {
//     return id.hashCode ^
//         userId.hashCode ^
//         totalPrice.hashCode ^
//         amountPaid.hashCode ^
//         isInstant.hashCode ^
//         isDropOff.hashCode ^
//         isDeal.hashCode ^
//         isCompleted.hashCode ^
//         scheduleStart.hashCode ^
//         scheduleEnd.hashCode ^
//         pickupTime.hashCode ^
//         carManagerId.hashCode ^
//         dealManagerId.hashCode ^
//         statusId.hashCode ^
//         policeEscortCount.hashCode ^
//         pickupAddress.hashCode ^
//         destinationAreaId.hashCode ^
//         destinationAddress.hashCode ^
//         createdAt.hashCode ^
//         updatedAt.hashCode ^
//         bookingType.hashCode ^
//         bookingTypeReadable.hashCode ^
//         destinationType.hashCode ^
//         carManager.hashCode ^
//         cars.hashCode ^
//         status.hashCode;
//   }

//   Car? get car => cars.isNotEmpty ? cars.first : carManager?.car;

//   int get numberOfCars => cars.isNotEmpty ? cars.length : 1;

//   bool get isFiveMinutesAgo {
//     final Duration difference = DateTime.now().difference(createdAt);
//     return difference.inMinutes <= 5;
//   }

//   bool get isApproved => bookingApprovedStatuses.contains(status.toLowerCase());

//   bool get isSuccessful =>
//       bookingSuccessStatuses.contains(status.toLowerCase());

//   bool get isPending => bookingPendingStatuses.contains(status.toLowerCase());

//   bool get isCanceled => bookingFailedStatuses.contains(status.toLowerCase());

//   bool get isOnTheWay => isFiveMinutesAgo && isApproved;

//   bool get isWithinTheDay {
//     final now = DateTime.now();
//     return createdAt.year == now.year &&
//         createdAt.month == now.month &&
//         createdAt.day == now.day;

//     // final now = DateTime.now();
//     // final startOfDay = DateTime(now.year, now.month, now.day);
//     // final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);
//     // return dateTime.isAfter(startOfDay) && dateTime.isBefore(endOfDay);
//   }
// }
