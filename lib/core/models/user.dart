import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:fleet_mobile/core/core.dart';

class User extends Equatable {
  final String id;
  final String authToken;
  // final int authTokenExp;
  final String firstName;
  final String? lastName;
  final String email;
  final String phoneNumber;
  final PaymentInfo? paymentInfo;
  final String? fcmToken;
  final Wallet? wallet;
  final Role role;
  final UserMetaData? metaData;

  const User({
    required this.id,
    required this.authToken,
    required this.firstName,
    this.lastName,
    required this.email,
    required this.phoneNumber,
    this.paymentInfo,
    this.fcmToken,
    this.wallet,
    required this.role,
    this.metaData,
  });

  @override
  List<Object?> get props {
    return [
      id,
      authToken,
      firstName,
      lastName,
      email,
      phoneNumber,
      paymentInfo,
      fcmToken,
      wallet,
      role,
      metaData,
    ];
  }

  User copyWith({
    String? id,
    String? authToken,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    PaymentInfo? paymentInfo,
    String? fcmToken,
    Wallet? wallet,
    Role? role,
    UserMetaData? metaData,
  }) {
    return User(
      id: id ?? this.id,
      authToken: authToken ?? this.authToken,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      paymentInfo: paymentInfo ?? this.paymentInfo,
      fcmToken: fcmToken ?? this.fcmToken,
      wallet: wallet ?? this.wallet,
      role: role ?? this.role,
      metaData: metaData ?? this.metaData,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'authToken': authToken,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phoneNumber': phoneNumber,
      'paymentInfo': paymentInfo?.toMap(),
      'fcmToken': fcmToken,
      'wallet': wallet?.toMap(),
      'role': role.value,
      'metaData': metaData?.toMap(),
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['_id'] ?? '',
      authToken: map['authToken'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'],
      email: map['email'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      paymentInfo: map['paymentInfo'] != null
          ? PaymentInfo.fromMap(map['paymentInfo'])
          : null,
      fcmToken: map['fcmToken'],
      wallet: map['walletBalance'] != null
          ? Wallet.fromMap(map['walletBalance'])
          : null,
      role: Role.fromString(map['role'] ?? ''),
      metaData: map['meta'] != null ? UserMetaData.fromMap(map['meta']) : null,
    );
  }

  factory User.defaultValue() => User(
        id: 'default-id',
        authToken: 'default-token',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+*************',
        paymentInfo: PaymentInfo(
            bankName: 'Default Bank',
            accountNumber: '**********',
            accountName: 'John Doe'),
        fcmToken: 'default-fcm-token',
        wallet: Wallet.defaultValue(),
        role: Role.unknown,
        metaData: UserMetaData.defaultValue(),
      );

  String toJson() => json.encode(toMap());

  factory User.fromJson(String source) => User.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';

  String get fullName => '$firstName $lastName';
  bool get hasSetupPayment =>
      paymentInfo?.accountNumber != null &&
      paymentInfo!.accountNumber.isNotEmpty;

  String get localPhone {
    if (phoneNumber.startsWith('+234')) {
      return '0${phoneNumber.substring(4)}';
    }
    return phoneNumber;
  }

  bool get isAdmin => role == Role.admin;
  bool get isSuperAdmin => role == Role.superAdmin;
  bool get isManager => role == Role.manager;
  bool get isUnknown => role == Role.unknown;
}

class PaymentInfo {
  final String bankName;
  final String accountNumber;
  final String accountName;

  PaymentInfo({
    required this.bankName,
    required this.accountNumber,
    required this.accountName,
  });

  Map<String, dynamic> toMap() {
    return {
      'bankName': bankName,
      'accountNumber': accountNumber,
      'accountName': accountName,
    };
  }

  factory PaymentInfo.fromMap(Map<String, dynamic> map) {
    return PaymentInfo(
      bankName: map['bankName'] ?? '',
      accountNumber: map['accountNumber'] ?? '',
      accountName: map['accountName'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory PaymentInfo.fromJson(String source) =>
      PaymentInfo.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}

class UserMetaData {
  final int totalBookings;
  final int approvedBookings;
  final int pendingBookings;
  final int declinedBookings;
  final int totalCars;
  final int availableCars;
  final int unavailableCars;

  UserMetaData({
    required this.totalBookings,
    required this.approvedBookings,
    required this.pendingBookings,
    required this.declinedBookings,
    required this.totalCars,
    required this.availableCars,
    required this.unavailableCars,
  });

  factory UserMetaData.fromMap(Map<String, dynamic> map) {
    return UserMetaData(
      totalBookings: map['totalBookings'] ?? 0,
      approvedBookings: map['approvedBookings'] ?? 0,
      pendingBookings: map['pendingBookings'] ?? 0,
      declinedBookings: map['declinedBookings'] ?? 0,
      totalCars: map['totalCars'] ?? 0,
      availableCars: map['availableCars'] ?? 0,
      unavailableCars: map['unavailableCars'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalBookings': totalBookings,
      'approvedBookings': approvedBookings,
      'pendingBookings': pendingBookings,
      'declinedBookings': declinedBookings,
      'totalCars': totalCars,
      'availableCars': availableCars,
      'unavailableCars': unavailableCars,
    };
  }

  String toJson() => json.encode(toMap());

  factory UserMetaData.fromJson(String source) =>
      UserMetaData.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';

  factory UserMetaData.defaultValue() => UserMetaData(
        totalBookings: 0,
        approvedBookings: 0,
        pendingBookings: 0,
        declinedBookings: 0,
        totalCars: 0,
        availableCars: 0,
        unavailableCars: 0,
      );
}
