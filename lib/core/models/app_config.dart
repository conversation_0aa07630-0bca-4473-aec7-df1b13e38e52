class AppConfig {
  final String baseUrl;
  final String googleApiKey;
  final ENVIRONMENT environment;
  AppConfig({
    required this.baseUrl,
    required this.googleApiKey,
    required this.environment,
  });

  AppConfig.fromJson(Map<Object, dynamic> map)
      : baseUrl = map['baseUrl'],
        googleApiKey = map['googleApiKey'],
        environment = ENVIRONMENT.parse(map['ENVIRONMENT']);
}

enum ENVIRONMENT {
  dev("dev"),
  prod("prod");

  final String name;
  const ENVIRONMENT(this.name);

  static final Map<String, ENVIRONMENT> _map = {
    dev.name: dev,
    prod.name: prod,
  };

  static ENVIRONMENT parse(String? flavor,
      {ENVIRONMENT defaultEnv = ENVIRONMENT.dev}) {
    return flavor != null ? _map[flavor] ?? defaultEnv : defaultEnv;
  }

  @override
  String toString() => name;
}
