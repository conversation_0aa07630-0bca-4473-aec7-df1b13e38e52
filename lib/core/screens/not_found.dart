import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class NotFoundScreen extends StatelessWidget {
  final String? message;

  const NotFoundScreen({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(kImgBrandIcon, alignment: Alignment.center),
              const YMargin(30),
              Text("Oops! Page not found! ", style: textTheme.titleLarge),
              const Y<PERSON>argin(20),
              Text(
                  message ??
                      "We are very sorry for inconvenience. It looks like you’re trying to access page that has been deleted or never existed. ",
                  style: textTheme.bodyMedium,
                  textAlign: TextAlign.center),
              const Y<PERSON>argin(30),
              LuxButton(
                text: "Back to Home",
                onTap: () => context.goNamed(homePath),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
