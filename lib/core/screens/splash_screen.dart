import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen(this.path, {super.key});

  final String? path;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    // Future.delayed(const Duration(milliseconds: 2000), () => handleAuth());
    handleAuth();
  }

  @override
  void dispose() {
    // SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
    //     overlays: SystemUiOverlay.values);
    super.dispose();
  }

  Future<void> handleAuth() async {
    final st = FlutterSecureStorage(aOptions: getAndroidOptions());
    final authNotifier = ref.read(authControllerProvider.notifier);
    final routerProvider = ref.read(routeProvider);

    String? userString;

    try {
      userString = await st.read(key: Keys.user);
    } catch (_) {
      await authNotifier.logout();
      routerProvider.router.goNamed(loginPath);
      return;
    }

    if (userString != null && userString.isNotEmpty) {
      final user = User.fromJson(userString);
      await authNotifier.login(user);
      // final route = widget.path == '/' ? homePath : widget.path;

      routerProvider.router.goNamed(homePath);
    } else {
      routerProvider.router.goNamed(loginPath);
    }
  }

  bool isTokenExpired(DateTime dateTime, int expirationSeconds) {
    int currentTimeMilliseconds = DateTime.now().millisecondsSinceEpoch;
    int expirationTimeMilliseconds =
        dateTime.millisecondsSinceEpoch + (expirationSeconds * 1000);

    return currentTimeMilliseconds >= expirationTimeMilliseconds;
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(dioUpdaterProvider);
    ref.watch(dioProvider);
    return Scaffold(
      body: Center(
        child: Image.asset(kImgBrandIcon),
      ),
    );
  }
}
