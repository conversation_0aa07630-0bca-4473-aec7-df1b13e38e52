import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/material.dart';

class FailureScreen extends StatelessWidget {
  final String message;
  final VoidCallback retry;

  const FailureScreen({super.key, required this.message, required this.retry});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: FailureBody(
        message: message,
        retry: retry,
      ),
    );
  }
}

class FailureBody extends StatelessWidget {
  const FailureBody({super.key, required this.message, required this.retry});

  final String message;
  final VoidCallback retry;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              message,
              style: textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 5),
            OutlinedButton(
              onPressed: retry,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Text(
                  'retry',
                  style: textTheme.bodyMedium?.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}

class FailureWidget extends StatelessWidget {
  const FailureWidget({
    super.key,
    required this.e,
    required this.retry,
    this.fullScreen = false,
    this.heightFactor = 0.77,
  });

  final dynamic e;
  final VoidCallback retry;
  final bool? fullScreen;
  final double? heightFactor;

  Widget child() {
    if (e is Failure) {
      return FailureBody(
        message: e.message,
        retry: retry,
      );
    }

    return FailureBody(
      message: 'Something went wrong',
      retry: retry,
    );
  }

  @override
  Widget build(BuildContext context) {
    return fullScreen!
        ? SizedBox(
            height: MediaQuery.sizeOf(context).height * heightFactor!,
            child: child(),
          )
        : child();
  }
}
