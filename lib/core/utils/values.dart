import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/material.dart';

typedef LuxValidators = String? Function(String?);

/// Form Styles - These will be created dynamically based on the theme
/// Use getFormBorder, getFocusedFormBorder, getErrorBorder, and getDefaultBorder functions
/// instead of these static variables

// Helper functions to get theme-aware form borders
OutlineInputBorder getFormBorder(BuildContext context) {
  return OutlineInputBorder(
    borderRadius: BorderRadius.circular(10),
    borderSide: BorderSide(color: Theme.of(context).colorScheme.onSurface),
  );
}

OutlineInputBorder getFocusedFormBorder(BuildContext context) {
  return OutlineInputBorder(
    borderRadius: BorderRadius.circular(10),
    borderSide: BorderSide(color: Theme.of(context).colorScheme.secondary),
  );
}

OutlineInputBorder getErrorBorder(BuildContext context) {
  return OutlineInputBorder(
    borderRadius: BorderRadius.circular(10),
    borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
  );
}

OutlineInputBorder getDefaultBorder(BuildContext context) {
  return OutlineInputBorder(
    borderRadius: BorderRadius.circular(8),
    borderSide: BorderSide(color: Theme.of(context).colorScheme.surface),
  );
}

// Legacy variables for backward compatibility
// These will be removed in a future update
OutlineInputBorder formBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(10),
    borderSide: const BorderSide(color: Color(0xffffffff))); // white
OutlineInputBorder focusedFormBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(10),
    borderSide: const BorderSide(color: Color(0xffffffff))); // white
OutlineInputBorder errorBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(10),
    borderSide: const BorderSide(color: Color(0xffE82139))); // red500
OutlineInputBorder defaultBorder = OutlineInputBorder(
  borderRadius: BorderRadius.circular(8),
  borderSide: const BorderSide(color: Color(0xff1d1c1b)), // black
);

String firebaseAppName =
    config.environment == ENVIRONMENT.dev ? 'LuxletDev' : 'Luxlet';

// booking approval statuses
const bookingApprovalApproved = ['approved'];
const bookingApprovalDeclined = ['decline'];
const bookingApprovalPending = ['pending'];

// booking statuses
const bookingStatusPaid = ['booked', 'inProgress', 'completed'];
const bookingStatusPending = ['pending'];
const bookingStatusCanceled = ['canceled'];

// custom made, used for determining when to show a booking as recently booked on the home screen
const recentlyBookedStatuses = ['pending', 'booked', 'inProgress'];
const recentlyBookedApprovalStatuses = ['approved', 'pending'];

const int retryDelayMilliseconds = 500;
const int kConnectTimeOut = 10;
const int kSendTimeOut = 10;
const int kReceiveTimeOut = 20;

const imgDir = 'assets/images';
const svgDir = 'assets/svgs';

const int maxImageSizeInMB = 2; // Maximum image size in MB
const int maxImageCount = 5; // Maximum number of images allowed

final List<String> kImages = [
  'http://res.cloudinary.com/kadismile/image/upload/v1726837029/1726836984913-hilux.jpg.jpg',
  'http://res.cloudinary.com/kadismile/image/upload/v1726837029/1726836984905-hilux2.jpg.jpg',
  'http://res.cloudinary.com/kadismile/image/upload/v1726837029/1726836984913-hilux.jpg.jpg',
  'http://res.cloudinary.com/kadismile/image/upload/v1726837029/1726836984905-hilux2.jpg.jpg',
];
