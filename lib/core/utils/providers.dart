import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:cote_network_logger/interceptor.dart';
import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/routes/router.dart';
import 'package:fleet_mobile/core/utils/config.dart';
import 'package:fleet_mobile/core/utils/values.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:riverpod/riverpod.dart';

final dioProvider = Provider<Dio>((ref) {
  final options = BaseOptions(
      baseUrl: config.baseUrl,
      connectTimeout: const Duration(seconds: kConnectTimeOut),
      sendTimeout: const Duration(seconds: kSendTimeOut),
      receiveTimeout: const Duration(seconds: kReceiveTimeOut),
      headers: {
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

  final dio = Dio(options);

  if (kDebugMode) {
    dio.interceptors.addAll([
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90,
      ),
      const CoteNetworkLogger(),
    ]);
  }

  return dio;
});

final dioUpdaterProvider = Provider<void>((ref) {
  const tokenKey = 'Bearer';
  const authKey = 'Authorization';

  final dio = ref.read(dioProvider);

  ref.listen(
    authControllerProvider.select((state) => state),
    (previous, next) {
      if (next.isLoggedIn) {
        dio.options.headers[authKey] = '$tokenKey ${next.authToken}';
        _fetchAuthData(ref);
      } else {
        dio.options.headers.remove(authKey);
      }
    },
  );
});

Future<void> _fetchAuthData(Ref ref) async {
  await Future.delayed(Duration.zero);
  ref.read(fleetControllerProvider.notifier).loadCars();
}

final routeProvider = Provider((ref) {
  // final authState = ref.watch(authControllerProvider);
  // return AppRouter(ref, authState);
  return AppRouter(ref);
});

final audioPlayerProvider = Provider<AudioPlayer>((ref) {
  final audioPlayer = AudioPlayer();
  ref.onDispose(() {
    audioPlayer.stop();
    audioPlayer.dispose();
  });
  return audioPlayer;
});

final playHornSoundProvider = Provider<void Function(AudioPlayer)>((ref) {
  return (audioPlayer) async {
    try {
      await audioPlayer.setSource(AssetSource('sound/horn.wav'));
      await audioPlayer.resume();
    } catch (e) {
      debugPrint('Error playing horn sound: $e');
    }
  };
});
