import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

Future<void> handleBackgroundMessage(RemoteMessage message) async {}

final pushNotificationProvider = Provider<PushNotification>((ref) {
  final appRouter = ref.read(routeProvider);
  return PushNotification(appRouter, ref);
});

class PushNotification {
  final AppRouter appRouter;
  final Ref ref;
  PushNotification(this.appRouter, this.ref) {
    initNotifications();
  }

  final _firebaseMessaging = FirebaseMessaging.instance;

  final _androidChannel = const AndroidNotificationChannel(
    'lux_fleet_notification_channel',
    'Lux Fleet Notifications',
    description:
        'This channel is used for LuxLetAfrica Fleet App notifications',
    importance: Importance.high,
    playSound: true,
    sound: RawResourceAndroidNotificationSound('horn'),
  );

  final _bookingChannel = const AndroidNotificationChannel(
    'lux_fleet_booking_channel',
    'Booking Notifications',
    description: 'Channel for booking-related notifications',
    importance: Importance.max,
    playSound: true,
    sound: RawResourceAndroidNotificationSound('horn'),
  );

  final _localNotifications = FlutterLocalNotificationsPlugin();

  // Store active booking notifications for persistence
  final Map<String, Timer> _activeBookingNotifications = {};

  void handleMessage(RemoteMessage? message) {
    if (message == null) return;

    // Check if it's a booking notification
    final data = message.data;
    if (data['type'] == 'booking_request') {
      _handleBookingNotification(message);
    }

    appRouter.router.goNamed(notificationPath);
  }

  void _handleBookingNotification(RemoteMessage message) {
    final bookingId = message.data['bookingId'];
    if (bookingId == null) return;

    // Start persistent notification for this booking
    _startPersistentBookingNotification(bookingId, message);
  }

  void _startPersistentBookingNotification(
      String bookingId, RemoteMessage message) {
    // Cancel any existing timer for this booking
    _activeBookingNotifications[bookingId]?.cancel();

    // Create a timer that shows notification every 30 seconds
    _activeBookingNotifications[bookingId] = Timer.periodic(
      const Duration(seconds: 30),
      (timer) {
        _showBookingNotification(message);
      },
    );

    // Show initial notification immediately
    _showBookingNotification(message);
  }

  void _showBookingNotification(RemoteMessage message) {
    final notification = message.notification;
    if (notification == null) return;

    _localNotifications.show(
      notification.hashCode,
      notification.title,
      notification.body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          _bookingChannel.id,
          _bookingChannel.name,
          channelDescription: _bookingChannel.description,
          importance: Importance.max,
          priority: Priority.high,
          playSound: true,
          sound: const RawResourceAndroidNotificationSound('horn'),
          ongoing: true, // Makes notification persistent
          autoCancel: false,
          icon: '@drawable/ic_launcher',
        ),
      ),
      payload: jsonEncode(message.toMap()),
    );
  }

  void stopBookingNotification(String bookingId) {
    _activeBookingNotifications[bookingId]?.cancel();
    _activeBookingNotifications.remove(bookingId);
  }

  Future initLocalNotification() async {
    const iOS = DarwinInitializationSettings();
    const android = AndroidInitializationSettings('@drawable/ic_launcher');
    const settings = InitializationSettings(android: android, iOS: iOS);

    await _localNotifications.initialize(
      settings,
      onDidReceiveNotificationResponse: (response) {
        final message = RemoteMessage.fromMap(jsonDecode(response.payload!));
        handleMessage(message);
      },
    );

    final platform = _localNotifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    await platform?.createNotificationChannel(_androidChannel);
    await platform?.createNotificationChannel(_bookingChannel);
  }

  Future initPushNotification() async {
    // IOS foreground notification
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // notification opened state
    FirebaseMessaging.instance.getInitialMessage().then(handleMessage);
    // background state
    FirebaseMessaging.onMessageOpenedApp.listen(handleMessage);
    FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);
    FirebaseMessaging.onMessage.listen((message) {
      final notification = message.notification;
      if (notification == null) return;
      _localNotifications.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
              _androidChannel.id, _androidChannel.name,
              channelDescription: _androidChannel.description,
              icon: '@drawable/ic_launcher'),
        ),
        payload: jsonEncode(message.toMap()),
      );
    });

    // Listen for token refresh
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
      log('FCM Token refreshed: $newToken');
      // Save the new token to your server
      sendTokenToServer(newToken);
    });
  }

  Future<void> initNotifications() async {
    await _firebaseMessaging.requestPermission(
        alert: true, badge: true, sound: true);
    // if (permission.authorizationStatus == AuthorizationStatus.authorized) {
    // final fcmToken = await getFcmToken();
    // print('Token: $fcmToken');

    final fcmToken = await getFcmToken();
    if (fcmToken.isNotEmpty) {
      log('Initial FCM Token: $fcmToken');
      sendTokenToServer(fcmToken);
    }

    initPushNotification();
    initLocalNotification();
    // }
  }

  Future<String> getFcmToken() async {
    try {
      return await _firebaseMessaging.getToken() ?? '';
    } catch (e) {
      return '';
    }
  }

  Future<void> sendTokenToServer(String token) async {
    final user = ref.read(authControllerProvider).user;

    if (user == null) return;

    ref
        .read(authControllerProvider.notifier)
        .updateUser(user.copyWith(fcmToken: token));

    final res = await ref
        .read(authServiceProvider)
        .updateUserProfile(user.id, {'fcmToken': token});

    res.when(
      (_) {
        // event tracking
      },
      (error) {
        // event tracking
      },
    );
  }
}
