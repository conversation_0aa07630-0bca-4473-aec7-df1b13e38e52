import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

extension Data on Map<String, TextEditingController> {
  Map<String, dynamic> data() {
    final res = <String, dynamic>{};
    for (MapEntry e in entries) {
      res.putIfAbsent(e.key, () => e.value?.text);
    }
    return res;
  }
}

extension DateTimeExtension on DateTime {
  String formattedDate() {
    final formattedDate =
        '${day.toString().padLeft(2, '0')}-${month.toString().padLeft(2, '0')}-$year';
    return formattedDate;
  }

  String timeAgoOrFormattedDate() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    if (isAfter(today)) {
      final difference = now.difference(this);
      if (difference.inMinutes < 60) {
        return '${difference.inMinutes} mins ago';
      } else {
        return '${difference.inHours} hrs ago';
      }
    } else {
      return formattedDate();
    }
  }

  String toShortDate() {
    String format =
        '${DateFormat.ABBR_MONTH} ${DateFormat.DAY}, ${DateFormat.YEAR}';
    return DateFormat(format).format(this);
  }
}

extension ListExtension<T> on List<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (final item in this) {
      if (test(item)) {
        return item;
      }
    }
    return null;
  }
}

extension IntSpacingExtension on int {
  SizedBox get height => SizedBox(height: toDouble());
  SizedBox get width => SizedBox(width: toDouble());
}

extension DoubleSpacingExtension on double {
  SizedBox get height => SizedBox(height: this);
  SizedBox get width => SizedBox(width: this);
}

extension IndexedMap<E> on Iterable<E> {
  Iterable<T> mapIndexed<T>(T Function(int index, E element) f) {
    var i = 0;
    return map((e) => f(i++, e));
  }
}

extension ThemeExtension on BuildContext {
  bool get isDark => Theme.of(this).brightness == Brightness.dark;
  bool get isLight => Theme.of(this).brightness == Brightness.light;
  ColorScheme get colorScheme => Theme.of(this).colorScheme;
  ThemeData get theme => Theme.of(this);
  TextTheme get textTheme => Theme.of(this).textTheme;
  AppBarTheme get appBarTheme => Theme.of(this).appBarTheme;
  ButtonThemeData get buttonTheme => Theme.of(this).buttonTheme;
  IconThemeData get iconTheme => Theme.of(this).iconTheme;
  InputDecorationTheme get inputDecorationTheme =>
      Theme.of(this).inputDecorationTheme;
  OutlinedButtonThemeData get outlinedButtonTheme =>
      Theme.of(this).outlinedButtonTheme;
  RadioThemeData get radioTheme => Theme.of(this).radioTheme;
  SliderThemeData get sliderTheme => Theme.of(this).sliderTheme;
  SwitchThemeData get switchTheme => Theme.of(this).switchTheme;
  TextSelectionThemeData get textSelectionTheme =>
      Theme.of(this).textSelectionTheme;
  TextButtonThemeData get textButtonTheme => Theme.of(this).textButtonTheme;
  TooltipThemeData get tooltipTheme => Theme.of(this).tooltipTheme;
  Typography get typography => Theme.of(this).typography;
}

extension OnTapExtension on Widget {
  Widget onTap(VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: this,
    );
  }

  Widget onTapNoFeedback(VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: this,
    );
  }
}
