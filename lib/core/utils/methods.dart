import 'dart:io';
import 'dart:math';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:intl/intl.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:url_launcher/url_launcher.dart';

import 'timestamp.dart';

DateTime? parseDate(date) {
  if (date is DateTime) {
    return date;
  } else if (date is num) {
    try {
      return DateTime.fromMillisecondsSinceEpoch(date.toInt());
    } catch (_) {
      return DateTime.fromMicrosecondsSinceEpoch(date.toInt());
    }
  } else if (date is String) {
    try {
      return DateTime.parse(date);
    } catch (error) {
      return null;
    }
  } else if (date is Timestamp) {
    return date.toDate();
  }

  try {
    return date.toDate();
  } catch (_) {
    return null;
  }
}

String toTitleCase(String? s) {
  if (s == null || s.isEmpty) return "";

  var result = s[0].toUpperCase();
  for (int i = 1; i < s.length; i++) {
    if (s[i - 1] == " ") {
      result = result + s[i].toUpperCase();
    } else {
      result = result + s[i];
    }
  }
  return result;
}

String capitalize(String? s) {
  if (s == null || s.isEmpty) return "";
  String lowercase = s.toLowerCase();
  if (lowercase.length == 1) lowercase.toUpperCase();
  return lowercase[0].toUpperCase() + lowercase.substring(1);
}

TextInputFormatter validInput() {
  return FilteringTextInputFormatter.allow(
    RegExp("[a-zA-Z0-9 ]"),
  );
}

TextInputFormatter validInputWithHyphen() {
  return FilteringTextInputFormatter.allow(
    RegExp("[a-zA-Z0-9 -]"),
  );
}

TextInputFormatter validNumbers() {
  return FilteringTextInputFormatter.digitsOnly;
}

Map<String, dynamic>? asStringKeyedMap(Map<dynamic, dynamic>? map) {
  if (map == null) return null;
  if (map is Map<String, dynamic>) {
    return map;
  } else {
    return Map<String, dynamic>.from(map);
  }
}

String encodeMap(Map data) {
  return data.keys
      .map((key) =>
          "${Uri.encodeComponent(key)}=${Uri.encodeComponent(data[key]?.toString() ?? '')}")
      .join("&");
}

Iterable<E> mapIndexed<E, T>(
    Iterable<T> items, E Function(int index, T item) f) sync* {
  var index = 0;

  for (final item in items) {
    yield f(index, item);
    index = index + 1;
  }
}

class CurrencyInputFormatter extends TextInputFormatter {
  final int decimalPlaces;

  CurrencyInputFormatter({this.decimalPlaces = 2});

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isEmpty) {
      return newValue.copyWith(text: '');
    } else if (newValue.text.compareTo(oldValue.text) != 0) {
      final int selectionIndexFromTheRight =
          newValue.text.length - newValue.selection.end;

      final formatter = NumberFormat("#,##0.${"#" * decimalPlaces}");
      final text = newValue.text.replaceAll(formatter.symbols.GROUP_SEP, '');

      String newString;

      if (text.contains('.')) {
        final decimalPlacesValue = text.split('.');
        final decimalOnly = decimalPlacesValue[1];
        final decimalTruncated =
            decimalOnly.substring(0, min(decimalPlaces, decimalOnly.length));
        final digitOnly = double.tryParse(decimalPlacesValue[0]) ?? 0.0;
        final formattedDigit = formatter.format(digitOnly);
        newString = '$formattedDigit.$decimalTruncated';
      } else {
        final num = int.parse(text);
        newString = formatter.format(num);
      }

      return TextEditingValue(
        text: newString,
        selection: TextSelection.collapsed(
            offset: newString.length - selectionIndexFromTheRight),
      );
    } else {
      return newValue;
    }
  }
}

class ExpiryDateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String formattedText = newValue.text;

    if (newValue.text.length == 3 && !newValue.text.contains('/')) {
      formattedText = '${newValue.text.substring(0, 2)}/'
          '${newValue.text.substring(2)}';
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}

class NoSpaceInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return newValue.copyWith(
      text: newValue.text.replaceAll(' ', ''), // Remove spaces
      selection: newValue.selection,
    );
  }
}

class AddSpaceFormatter extends TextInputFormatter {
  final int spaceAfter;

  AddSpaceFormatter(this.spaceAfter);

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    var text = newValue.text;

    if (newValue.selection.baseOffset == 0) {
      return newValue;
    }

    var buffer = StringBuffer();
    for (int i = 0; i < text.length; i++) {
      buffer.write(text[i]);
      var nonZeroIndex = i + 1;
      if (nonZeroIndex % spaceAfter == 0 && nonZeroIndex != text.length) {
        buffer.write(
            ' '); // Replace this with anything you want to put after each 4 numbers
      }
    }

    var string = buffer.toString();
    return newValue.copyWith(
        text: string,
        selection: TextSelection.collapsed(offset: string.length));
  }
}

String formatPhoneNumber(String phoneNumber) {
  // Remove any non-digit characters from the phone number
  String cleanedNumber = phoneNumber.replaceAll(RegExp(r'\D'), '');

  // If the cleaned number starts with '234', remove it
  if (cleanedNumber.startsWith('234')) {
    cleanedNumber = cleanedNumber.substring(3);
  }

  // If the cleaned number starts with '0', return it as is
  if (cleanedNumber.startsWith('0')) {
    return cleanedNumber;
  }

  // If the cleaned number doesn't start with '0', add '0' to the beginning
  return '0$cleanedNumber';
}

Future<String> getDeviceFingerprint() async {
  try {
    final deviceInfo = DeviceInfoPlugin();
    String? fingerprint;

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      fingerprint = androidInfo.fingerprint;
    }

    if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      fingerprint = iosInfo.identifierForVendor;
    }

    return fingerprint ?? '';
  } catch (e) {
    return '';
  }
}

/// Determines if a given string contains only numeric digits (0-9).
///
/// Example usage:
/// ```dart
/// print(isNumeric('123456')); // Output: true
/// print(isNumeric('123a456')); // Output: false
/// print(isNumeric('12.34')); // Output: false
/// print(isNumeric('')); // Output: false
/// ```
bool isNumeric(String text) {
  final numberRegExp = RegExp(r'^[0-9]+$');
  return numberRegExp.hasMatch(text);
}

/// Validates if a given string is in the format of a standard email address.
///
/// Example usage:
/// ```dart
/// print(isEmail('<EMAIL>')); // Output: true
/// print(isEmail('<EMAIL>')); // Output: true
/// print(isEmail('invalid-email@domain')); // Output: false
/// print(isEmail('user@.com')); // Output: false
/// ```
bool isEmail(String text) {
  final emailRegExp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
  return emailRegExp.hasMatch(text);
}

/// Checks if a given string is a valid number, which may include an optional decimal point.
///
/// Example usage:
/// ```dart
/// print(isNumber('123')); // Output: true
/// print(isNumber('123.45')); // Output: true
/// print(isNumber('123.45.67')); // Output: false
/// print(isNumber('abc')); // Output: false
/// ```
bool isNumber(String text) {
  final numberRegExp = RegExp(r'^\d+(\.\d+)?$');
  return numberRegExp.hasMatch(text);
}

String generateStrongPassword([int length = 8]) {
  const String uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const String lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
  const String digits = '0123456789';
  const String specialChars = '!@#\$%^&*()_+{}[]|:;"<>,.?/';

  String allChars = uppercaseChars + lowercaseChars + digits + specialChars;
  Random random = Random.secure();

  // Ensure at least one character from each category
  String password = uppercaseChars[random.nextInt(uppercaseChars.length)] +
      lowercaseChars[random.nextInt(lowercaseChars.length)] +
      digits[random.nextInt(digits.length)] +
      specialChars[random.nextInt(specialChars.length)];

  // Generate remaining characters
  List<String> passwordCharacters = List.generate(length - 4, (index) {
    int randomIndex = random.nextInt(allChars.length);
    return allChars[randomIndex];
  });

  password += passwordCharacters.join();
  return password;
}

String generateRandomDigits([int length = 10]) {
  const String digits = '0123456789';
  Random random = Random.secure();

  String randomDigits = List.generate(length, (index) {
    int randomIndex = random.nextInt(digits.length);
    return digits[randomIndex];
  }).join();

  return randomDigits;
}

Future<String> getPasswordForUser(String phoneNumber) async {
  try {
    final st = FlutterSecureStorage(aOptions: getAndroidOptions());
    final savedPassword = await st.read(key: Keys.password);
    final savedPhoneNumber = await st.read(key: Keys.phone) ?? '';

    if (savedPassword != null &&
        savedPassword.isNotEmpty &&
        (phoneNumber == savedPhoneNumber)) {
      return savedPassword;
    } else {
      final generatedPassword = _generate(phoneNumber);
      return generatedPassword;
    }
  } catch (_) {
    final generatedPassword = _generate(phoneNumber);
    return generatedPassword;
  }
}

Future<String> _generate(String phoneNumber) async {
  final st = FlutterSecureStorage(aOptions: getAndroidOptions());
  final generatedPassword = generateStrongPassword();
  await st.write(key: Keys.password, value: generatedPassword);
  await st.write(key: Keys.phone, value: phoneNumber);
  return generatedPassword;
}

AndroidOptions getAndroidOptions() =>
    const AndroidOptions(encryptedSharedPreferences: true);

void openUri(String uri) async {
  final url = Uri.parse(uri);
  if (await canLaunchUrl(url)) {
    await launchUrl(url);
  } else {
    throw 'Could not launch $uri';
  }
}

num? toNumber(dynamic value) {
  if (value is String || value is int) return num.parse(value.toString());
  return value;
}

double? toDouble(dynamic value) {
  if (value is String || value is int || value is num) {
    return double.parse(value.toString());
  }
  return value;
}

bool toBool(dynamic value) {
  if (value is bool) return value;

  late int val;

  if (value is String) {
    try {
      val = int.parse(value);
    } catch (e) {
      // [value] might be the string value `true` or `false`
      if (value.toLowerCase() == 'true') {
        return true;
      } else {
        return false;
      }
    }
  } else {
    val = value;
  }

  return val == 0 ? false : true;
}

void showConfirmationDialog(
  BuildContext context,
  String title,
  String content,
  String actionText, {
  required VoidCallback onConfirm,
  Color? actionTextColor,
}) {
  final textTheme = Theme.of(context).textTheme;
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(title),
        content: Text(
          content,
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            // style: TextButton.styleFrom(
            //   backgroundColor: Palette.k040302,
            // ),
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel', style: textTheme.bodyLarge),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Palette.k040302,
            ),
            onPressed: onConfirm,
            child: Text(actionText,
                style: textTheme.bodyLarge?.copyWith(color: actionTextColor)),
          ),
        ],
      );
    },
  );
}

class Uuid {
  final Random _random = Random();

  String generateV4() {
    // Generate xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx / 8-4-4-4-12.
    final int special = 8 + _random.nextInt(4);

    return '${_bitsDigits(16, 4)}${_bitsDigits(16, 4)}-'
        '${_bitsDigits(16, 4)}-'
        '4${_bitsDigits(12, 3)}-'
        '${_printDigits(special, 1)}${_bitsDigits(12, 3)}-'
        '${_bitsDigits(16, 4)}${_bitsDigits(16, 4)}${_bitsDigits(16, 4)}';
  }

  String _bitsDigits(int bitCount, int digitCount) =>
      _printDigits(_generateBits(bitCount), digitCount);

  int _generateBits(int bitCount) => _random.nextInt(1 << bitCount);

  String _printDigits(int value, int count) =>
      value.toRadixString(16).padLeft(count, '0');
}

Dio createDioInstance({
  String? baseUrl,
  String? authToken,
  int? connectTimeout,
  int? sendTimeout,
  int? receiveTimeout,
}) {
  final options = BaseOptions(
    connectTimeout: Duration(seconds: connectTimeout ?? kConnectTimeOut),
    sendTimeout: Duration(seconds: sendTimeout ?? kSendTimeOut),
    receiveTimeout: Duration(seconds: receiveTimeout ?? kReceiveTimeOut),
    headers: {
      HttpHeaders.acceptHeader: 'application/json',
      HttpHeaders.contentTypeHeader: 'application/json',
    },
  );

  // Include baseUrl only if it's not null
  if (baseUrl != null) {
    options.baseUrl = baseUrl;
  }

  // Include Authorization header only if token is not null
  if (authToken != null) {
    options.headers['Authorization'] = 'Bearer $authToken';
  }

  final dio = Dio(options);

  if (kDebugMode) {
    dio.interceptors.add(PrettyDioLogger(
      requestHeader: true,
      requestBody: true,
      responseBody: true,
      responseHeader: false,
      error: true,
      compact: true,
      enabled: kDebugMode,
    ));
  }

  return dio;
}

String randomString(int length) {
  const characters =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  Random random = Random();

  return String.fromCharCodes(Iterable.generate(
    length,
    (_) => characters.codeUnitAt(random.nextInt(characters.length)),
  ));
}
