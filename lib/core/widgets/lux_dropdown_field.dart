import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/material.dart';
import 'package:wc_form_validators/wc_form_validators.dart';

class LuxDropdownField<T> extends StatefulWidget {
  final String? hint;
  final List<T> options;
  final T? initialValue;
  final String? Function(T?) getLabel;
  final String? Function(T?)? getData;
  final void Function(T?)? onChanged;
  final TextEditingController? dropDownFieldController;
  final bool? readonly;
  final bool? focusedBorder;
  final bool? disabled;
  final String? title;
  final bool? isRequired;
  final bool? searchAutofocus;
  final bool? enableSearch;
  final TextInputType? keyboardType;
  final bool? filled;
  final Color? filledColor;
  final VoidCallback? onTap;

  const LuxDropdownField({
    super.key,
    this.hint = 'Please select an Option',
    this.options = const [],
    required this.getLabel,
    this.getData,
    this.initialValue,
    this.onChanged,
    this.dropDownFieldController,
    this.readonly = false,
    this.focusedBorder = false,
    this.disabled = false,
    this.title,
    this.isRequired = true,
    this.searchAutofocus = true,
    this.enableSearch = true,
    this.keyboardType = TextInputType.text,
    this.filled,
    this.filledColor,
    this.onTap,
  });

  @override
  State<LuxDropdownField<T>> createState() => LuxDropdownFieldState<T>();
}

class LuxDropdownFieldState<T> extends State<LuxDropdownField<T>> {
  FocusNode searchFocusNode = FocusNode();
  FocusNode textFieldFocusNode = FocusNode();
  late SingleValueDropDownController _cnt;

  @override
  void initState() {
    super.initState();
    widget.dropDownFieldController?.addListener(() {
      reset();
    });

    if (widget.initialValue != null) {
      _cnt = SingleValueDropDownController(
        data: DropDownValueModel(
          name: widget.getLabel(widget.initialValue) ?? '',
          value: widget.initialValue,
        ),
      );
    } else {
      _cnt = SingleValueDropDownController();
    }
  }

  reset() {
    if (widget.dropDownFieldController?.text.isEmpty == true) {
      _cnt.clearDropDown();
    }
  }

  final isRequired = Validators.required('Required');

  LuxValidators? validators() {
    return widget.isRequired! ? Validators.compose([isRequired]) : null;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final child = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        if (widget.title != null) ...[
          Text(
            widget.title!,
          ),
          const YMargin(6),
        ],
        GestureDetector(
          onTap: () {},
          child: DropDownTextField(
            controller: _cnt,
            clearOption: false,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            textFieldFocusNode: textFieldFocusNode,
            searchFocusNode: searchFocusNode,
            searchAutofocus: widget.searchAutofocus!,
            // dropDownItemCount: 8,
            searchShowCursor: false,
            enableSearch: widget.enableSearch!,
            searchKeyboardType: widget.keyboardType,
            validator: validators(),
            dropDownIconProperty: IconProperty(
              icon: Icons.expand_more,
            ),
            // dropdownColor: Palette.k0C0C0C,
            // textStyle: textTheme.bodyLarge?.copyWith(
            //   fontWeight: FontWeight.w600,
            // ),
            listTextStyle: textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            dropDownList: widget.options.map(
              (T option) {
                return DropDownValueModel(
                  value: option,
                  name: '${widget.getLabel(option)}',
                );
              },
            ).toList(),
            onChanged: (val) {
              final data = widget.getData != null
                  ? widget.getData!(val.value as T)
                  : widget.getLabel(val.value as T);
              widget.dropDownFieldController?.text = data!;

              setState(() {
                // rebuild
              });

              if (widget.onChanged != null) {
                widget.onChanged!(val.value);
              }
            },
            textFieldDecoration: InputDecoration(
              hintText: widget.hint,
              hintStyle: textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 128)),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              enabledBorder: widget.focusedBorder!
                  ? getFocusedFormBorder(context)
                  : getFormBorder(context),
              focusedBorder: getFocusedFormBorder(context),
              border: widget.focusedBorder!
                  ? getFocusedFormBorder(context)
                  : getFormBorder(context),
              filled: widget.filled ??
                  widget.dropDownFieldController?.text.isNotEmpty,
              fillColor: widget.disabled!
                  ? widget.filledColor?.withValues(alpha: 128) ??
                      Theme.of(context)
                          .colorScheme
                          .surface
                          .withValues(alpha: 128)
                  : widget.filledColor ?? Theme.of(context).colorScheme.surface,
            ),
          ),
        ),
        const YMargin(16)
      ],
    );

    return widget.readonly == true ? IgnorePointer(child: child) : child;
  }

  @override
  void dispose() {
    _cnt.dispose();
    super.dispose();
  }
}
