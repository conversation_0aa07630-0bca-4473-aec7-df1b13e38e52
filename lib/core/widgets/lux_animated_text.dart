import 'package:flutter/material.dart';

class LuxAnimatedText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final AnimationController? controller;

  const LuxAnimatedText(
    this.text, {
    super.key,
    this.style,
    this.controller,
  });

  @override
  LuxAnimatedTextState createState() => LuxAnimatedTextState();
}

class LuxAnimatedTextState extends State<LuxAnimatedText>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ??
        AnimationController(
          vsync: this,
          duration: const Duration(milliseconds: 400),
          animationBehavior: AnimationBehavior.preserve,
        );

    _animation = CurvedAnimation(
      parent: _controller!,
      curve: Curves.easeOutCirc,
    );

    _controller?.forward();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final fontSize = _animation.value * (widget.style?.fontSize ?? 30);
        final textStyle = widget.style?.copyWith(fontSize: fontSize) ??
            TextStyle(fontSize: fontSize);

        return Text(
          widget.text,
          style: textStyle,
        );
      },
    );
  }
}
