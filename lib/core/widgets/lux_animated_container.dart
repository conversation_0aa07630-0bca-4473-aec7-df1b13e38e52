import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

enum ImageType { asset, network }

class LuxAnimatedContainer extends StatefulWidget {
  final Widget? child;
  final List<String> pngImagesPath;
  final void Function(int)? onIndexChanged;
  final int? milliseconds;
  final BoxFit? fit;
  final BorderRadiusGeometry? borderRadius;
  final AnimationController? controller;
  final ImageType? type;
  final bool? useBackgroundRendering;

  const LuxAnimatedContainer({
    super.key,
    this.child,
    required this.pngImagesPath,
    this.onIndexChanged,
    this.milliseconds = 3000,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.controller,
    this.type = ImageType.asset,
    this.useBackgroundRendering = true,
  });

  @override
  LuxAnimatedContainerState createState() => LuxAnimatedContainerState();
}

class LuxAnimatedContainerState extends State<LuxAnimatedContainer>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  late Animation<int> _imageIndex;

  List<String> get backgroundImages => widget.pngImagesPath;

  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ??
        AnimationController(
          vsync: this,
          duration: Duration(milliseconds: widget.milliseconds!),
          animationBehavior: AnimationBehavior.preserve,
        );

    _imageIndex = IntTween(
      begin: _currentIndex,
      end: _currentIndex,
    ).animate(_controller!);

    _controller?.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        final newIndex = (_currentIndex + 1) % backgroundImages.length;

        setState(() {
          if (widget.onIndexChanged != null) {
            widget.onIndexChanged!(newIndex);
          }
          _currentIndex = newIndex;
          _imageIndex = IntTween(
            begin: _currentIndex,
            end: newIndex,
          ).animate(_controller!);
          _controller?.reset();
          _controller?.forward();
        });
      }
    });

    _controller?.forward();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.sizeOf(context).width;
    final height = MediaQuery.sizeOf(context).height;
    return Stack(
      children: [
        if (widget.useBackgroundRendering!)
          AnimatedBuilder(
            animation: _controller!,
            builder: (context, child) {
              return AnimatedContainer(
                duration: Duration.zero,
                curve: Curves.easeInOutCirc,
                decoration: BoxDecoration(
                  image: widget.type! == ImageType.asset
                      ? DecorationImage(
                          image:
                              AssetImage(backgroundImages[_imageIndex.value]),
                          fit: widget.fit,
                        )
                      : DecorationImage(
                          image: CachedNetworkImageProvider(
                            backgroundImages[_imageIndex.value],
                          ),
                          fit: widget.fit,
                        ),
                  borderRadius: widget.borderRadius,
                ),
              );
            },
          )
        else
          AnimatedBuilder(
            animation: _controller!,
            builder: (context, child) {
              return AnimatedContainer(
                duration: Duration.zero,
                curve: Curves.easeInOutCirc,
                decoration: BoxDecoration(
                  borderRadius: widget.borderRadius,
                ),
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.only(top: height / 8),
                    child: widget.type! == ImageType.asset
                        ? Image.asset(
                            backgroundImages[_imageIndex.value],
                            fit: widget.fit,
                            width: width,
                            height: height / 2.5,
                          )
                        : CachedNetworkImage(
                            imageUrl: backgroundImages[_imageIndex.value],
                            fit: widget.fit,
                          ),
                  ),
                ),
              );
            },
          ),
        if (widget.child != null) widget.child!,
      ],
    );
  }
}
