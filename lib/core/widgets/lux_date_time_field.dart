import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

enum FieldType { date, time }

class LuxDateTimeField<T> extends StatelessWidget {
  const LuxDateTimeField({
    super.key,
    required this.fieldType,
    this.title,
    this.hint,
    this.textFieldController,
    this.isRequired = false,
    this.filled = true,
    this.initialValue,
    this.startDate,
    this.onChanged,
  })  : assert(
            T == DateTime || T == TimeOfDay, "T must be DateTime or TimeOfDay");

  final FieldType fieldType;
  final String? title;
  final String? hint;
  final TextEditingController? textFieldController;
  final bool? isRequired;
  final bool? filled;
  final String? initialValue;
  final DateTime? startDate;
  final Function(T, [DateTime?])? onChanged;

  Future<void> _selectDate(BuildContext context) async {
    final textTheme = Theme.of(context).textTheme;
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: startDate ?? DateTime.now(),
      firstDate: startDate ?? DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 93)),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.dark(useMaterial3: true).copyWith(
            textTheme: textTheme,
            hintColor: Palette.kFFB800,
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Palette.kFFFFFF,
                textStyle: textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != DateTime.now()) {
      textFieldController?.text = picked.toLocal().toString().split(' ')[0];
      if (onChanged != null) onChanged!(picked as T);
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final textTheme = Theme.of(context).textTheme;
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.dark(useMaterial3: true).copyWith(
            textTheme: textTheme,
            hintColor: Palette.kFFB800,
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Palette.kFFFFFF,
                textStyle: textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      if (context.mounted) {
        final date = convertTimeOfDayToDateTime(picked);
        textFieldController?.text = date.toLocal().toString().split(' ')[0];
        if (onChanged != null) {
          onChanged!(picked as T, date);
        }
      }
    }
  }

  DateTime convertTimeOfDayToDateTime(TimeOfDay timeOfDay) {
    final now = DateTime.now();
    return DateTime(
      now.year,
      now.month,
      now.day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
  }

  @override
  Widget build(BuildContext context) {
    return LuxTextField(
      hint: hint,
      title: title,
      readonly: true,
      onTap: () => fieldType == FieldType.date
          ? _selectDate(context)
          : _selectTime(context),
      initialValue: initialValue,
      textFieldController: textFieldController,
      textFieldType: TextFieldType.text,
      filled: filled,
      isRequired: isRequired,
      inputFormatters: [validNumbers()],
      suffixIcon: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SvgPicture.asset(
            fieldType == FieldType.date ? kSvgCalenderIcon : kSvgClockIcon),
      ),
    );
  }
}
