import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart';

class LuxAddressField extends StatefulWidget {
  const LuxAddressField({
    super.key,
    this.hint = 'Enter address',
    this.isRequired = false,
    this.title = 'Address',
    this.controller,
    this.initialValue,
    this.onTap,
    this.onLocationSelected,
    this.ctx,
  });

  final String? title;
  final String? hint;
  final bool? isRequired;
  final TextEditingController? controller;
  final String? initialValue;
  final VoidCallback? onTap;
  final Function(LuxAddress?)? onLocationSelected;
  final BuildContext? ctx;

  @override
  State<LuxAddressField> createState() => _LuxAddressFieldState();
}

class _LuxAddressFieldState extends State<LuxAddressField> {
  late FlutterGooglePlacesSdk _places;

  @override
  void initState() {
    super.initState();
    _places = FlutterGooglePlacesSdk(config.googleApiKey);
  }

  @override
  Widget build(BuildContext context) {
    final buildContext = widget.ctx ?? context;
    return LuxTextField(
      readonly: widget.isRequired,
      isRequired: true,
      hint: widget.hint,
      title: widget.title,
      textFieldType: TextFieldType.text,
      textFieldController: widget.controller,
      onTap: () async {
        if (widget.onTap != null) widget.onTap!();
        final address =
            await _handleAddressLocation(widget.controller, buildContext);
        widget.controller?.text = address?.address ?? '';
        if (widget.onLocationSelected != null) {
          widget.onLocationSelected!(address);
        }
      },
    );
  }

  Future<LuxAddress?> _handleAddressLocation(
    TextEditingController? controller,
    BuildContext context,
  ) async {
    try {
      // Show places autocomplete
      final result = await _showPlacesAutocomplete(context);
      if (result != null && context.mounted) {
        return await _getPlaceDetails(result.placeId, context);
      }
    } catch (e) {
      Toast.error('Error searching for places: ${e.toString()}');
    }
    return null;
  }

  // Replace the _showPlacesAutocomplete method to use a full page route instead of a bottom sheet

  Future<AutocompletePrediction?> _showPlacesAutocomplete(
      BuildContext context) async {
    return await Navigator.of(context, rootNavigator: true)
        .push<AutocompletePrediction>(
      MaterialPageRoute(
        fullscreenDialog: true,
        builder: (context) => Scaffold(
          backgroundColor: Palette.k0C0C0C,
          appBar: AppBar(
            backgroundColor: Palette.k0C0C0C,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              'Search Address',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Palette.kFFEABC,
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
          body: _PlacesAutocompleteWidget(
            places: _places,
            hint: widget.hint ?? 'Enter address',
          ),
        ),
      ),
    );
  }

  Future<LuxAddress?> _getPlaceDetails(
      String placeId, BuildContext context) async {
    try {
      Loader.show(context);

      final response = await _places.fetchPlace(
        placeId,
        fields: [
          PlaceField.Name,
          PlaceField.AddressComponents,
          PlaceField.Location,
        ],
      );

      Loader.hide();
      if (response.place != null) {
        return LuxAddress.fromPlace(response.place!);
      } else {
        Toast.error('Place details not found');
        return null;
      }
    } catch (e) {
      Loader.hide();
      Toast.error('Unable to extract address. Please try again');
      return null;
    }
  }
}

class _PlacesAutocompleteWidget extends StatefulWidget {
  final FlutterGooglePlacesSdk places;
  final String hint;

  const _PlacesAutocompleteWidget({
    required this.places,
    required this.hint,
  });

  @override
  State<_PlacesAutocompleteWidget> createState() =>
      _PlacesAutocompleteWidgetState();
}

class _PlacesAutocompleteWidgetState extends State<_PlacesAutocompleteWidget> {
  final TextEditingController _searchController = TextEditingController();
  List<AutocompletePrediction> _predictions = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Palette.k0C0C0C,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          // Container(
          //   margin: const EdgeInsets.only(top: 8),
          //   height: 4,
          //   width: 40,
          //   decoration: BoxDecoration(
          //     color: Colors.grey[400],
          //     borderRadius: BorderRadius.circular(2),
          //   ),
          // ),
          // // Header
          // Padding(
          //   padding: const EdgeInsets.all(16),
          //   child: Row(
          //     children: [
          //       Expanded(
          //         child: Text(
          //           'Search Address',
          //           style: Theme.of(context).textTheme.headlineSmall?.copyWith(
          //                 color: Palette.kFFEABC,
          //                 fontWeight: FontWeight.w600,
          //               ),
          //         ),
          //       ),
          //       IconButton(
          //         onPressed: () => Navigator.of(context).pop(),
          //         icon: const Icon(Icons.close, color: Colors.white),
          //       ),
          //     ],
          //   ),
          // ),
          // Search field
          const YMargin(10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              autofocus: true,
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: widget.hint,
                hintStyle: const TextStyle(color: Colors.grey),
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                filled: true,
                fillColor: Palette.k1D1C1B,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
              ),
              onChanged: _onSearchChanged,
            ),
          ),
          const SizedBox(height: 16),
          // Results
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Palette.kFFB800),
                  )
                : ListView.builder(
                    itemCount: _predictions.length,
                    itemBuilder: (context, index) {
                      final prediction = _predictions[index];
                      return ListTile(
                        leading: const Icon(Icons.location_on,
                            color: Palette.kFFB800),
                        title: Text(
                          prediction.primaryText,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text(
                          prediction.secondaryText,
                          style: const TextStyle(color: Colors.grey),
                        ),
                        onTap: () => Navigator.of(context).pop(prediction),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  void _onSearchChanged(String query) async {
    if (query.isEmpty) {
      setState(() {
        _predictions = [];
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await widget.places.findAutocompletePredictions(
        query,
        countries: ['NG'], // Restrict to Nigeria
      );

      setState(() {
        _predictions = response.predictions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _predictions = [];
        _isLoading = false;
      });
      Toast.error('Error searching places: ${e.toString()}');
    }
  }
}

// import 'dart:developer';

// import 'package:fleet_mobile/core/core.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_google_places/flutter_google_places.dart';
// import 'package:google_api_headers/google_api_headers.dart';
// import 'package:google_maps_webservice/places.dart';

// class LuxAddressField extends StatelessWidget {
//   const LuxAddressField({
//     super.key,
//     this.hint = 'Enter address',
//     this.isRequired = false,
//     this.title = 'Address',
//     this.controller,
//     this.initialValue,
//     this.onTap,
//     this.onLocationSelected,
//     this.filled,
//     // LuxAddressField is used within LuxLoader.show(), which doesn't have a direct context.
//     // Due to this limitation, the Google location plugin throws a 'context does not have a navigator' error
//     // when attempting to navigate using Navigator.of(context). To mitigate this temporarily,
//     // this 'ctx' parameter is utilized to handle the address location method that opens the Google Places picker.
//     // TODO: Remove this 'ctx' parameter when LuxLoader.show() context issue is resolved.
//     this.ctx,
//   });

//   final String? title;
//   final String? hint;
//   final bool? isRequired;
//   final TextEditingController? controller;
//   final String? initialValue;
//   final VoidCallback? onTap;
//   final Function(LuxAddress?)? onLocationSelected;
//   final BuildContext? ctx;
//   final bool? filled;

//   @override
//   Widget build(BuildContext context) {
//     final buildContext = ctx ?? context;
//     return LuxTextField(
//       readonly: isRequired,
//       isRequired: true,
//       filled: filled,
//       hint: hint,
//       title: title,
//       textFieldType: TextFieldType.text,
//       textFieldController: controller,
//       onTap: () async {
//         try {
//           if (onTap != null) onTap!();
//           final address = await handleAddressLocation(controller, buildContext);
//           controller?.text = address?.address ?? '';
//           if (onLocationSelected != null) onLocationSelected!(address);
//         } catch (e) {
//           log('$e');
//         }
//       },
//     );
//   }

//   Future<LuxAddress?> handleAddressLocation(
//     TextEditingController? controller,
//     BuildContext context,
//   ) async {
//     Prediction? p = await PlacesAutocomplete.show(
//       context: context,
//       apiKey: config.googleApiKey,
//       onError: (res) {
//         Toast.error(res.errorMessage ?? 'Error');
//       },
//       // mode: Mode.overlay,
//       language: "en",
//       decoration: InputDecoration(
//         hintText: hint,
//         border: InputBorder.none,
//         focusedBorder: InputBorder.none,
//         enabledBorder: InputBorder.none,
//         errorBorder: InputBorder.none,
//         disabledBorder: InputBorder.none,
//         focusedErrorBorder: InputBorder.none,
//       ),
//       types: [],
//       components: [Component(Component.country, "ng")],
//       strictbounds: false,
//       radius: 100000,
//       sessionToken: Uuid().generateV4(),
//     );

//     if (context.mounted) {
//       return await displayPrediction(p, context);
//     } else {
//       return null;
//     }
//   }

//   Future<LuxAddress?> displayPrediction(
//     Prediction? p,
//     BuildContext context,
//   ) async {
//     FocusScope.of(context).requestFocus(FocusNode());

//     if (p != null) {
//       Loader.show(context);

//       try {
//         GoogleMapsPlaces places = GoogleMapsPlaces(
//           apiKey: config.googleApiKey,
//           apiHeaders: await const GoogleApiHeaders().getHeaders(),
//         );
//         PlacesDetailsResponse detail =
//             await places.getDetailsByPlaceId(p.placeId!);
//         final address = LuxAddress.fromPlaceDetail(detail.result);
//         Loader.hide();
//         return address;
//       } catch (e) {
//         Loader.hide();
//         Toast.error('Unable to extract address. Please try again');
//         return null;
//       }
//     }

//     return null;
//   }
// }
