import 'package:cached_network_image/cached_network_image.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';

class LuxNetworkImage extends StatelessWidget {
  const LuxNetworkImage(this.imageUrl, {super.key, this.fit});

  final String? imageUrl;
  final BoxFit? fit;

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: '$imageUrl',
      fit: fit,
      progressIndicatorBuilder: (context, url, downloadProgress) => Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: Center(
            child: CircularProgressIndicator(
              value: downloadProgress.progress,
              valueColor: const AlwaysStoppedAnimation<Color>(Palette.kFFFFFF),
            ),
          ),
        ),
      ),
      errorWidget: (context, url, error) => const Icon(Icons.error),
    );
  }
}
