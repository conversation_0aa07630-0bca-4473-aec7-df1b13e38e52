import 'package:flutter/widgets.dart';

class YMargin extends StatelessWidget {
  const YMargin(this.height, {super.key});

  final double height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(height: height);
  }
}

class XMargin extends StatelessWidget {
  const XMargin(this.width, {super.key});

  final double width;

  @override
  Widget build(BuildContext context) {
    return SizedBox(width: width);
  }
}
