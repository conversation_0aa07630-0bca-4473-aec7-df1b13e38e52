import 'package:flutter/material.dart';

class LuxContainer extends StatelessWidget {
  const LuxContainer({
    super.key,
    this.child,
    this.margin,
    this.padding,
    this.height,
    this.color,
  });

  final Widget? child;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double? height;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: height,
      margin: margin ?? const EdgeInsets.only(top: 20),
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: BoxDecoration(
        color: color ?? theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: child,
    );
  }
}
