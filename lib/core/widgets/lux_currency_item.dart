import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CurrencyItem extends StatelessWidget {
  final num? amount;
  final int decimalDigits;
  final TextStyle? style;

  const CurrencyItem(
    this.amount, {
    super.key,
    this.decimalDigits = 0,
    this.style,
  });

  static String formatAmount(BuildContext context, num amount,
      [int decimalDigits = 0]) {
    final NumberFormat formatter = _formatter(context, decimalDigits);
    return formatter.format(amount);
  }

  static String formatAmountWithSymbol(BuildContext context, num amount,
      [int decimalDigits = 0]) {
    final NumberFormat formatter = _formatter(context, decimalDigits);
    return 'N${formatter.format(amount)}';
  }

  static String currencySymbol(BuildContext context, String currency) {
    return _formatter(context).simpleCurrencySymbol(currency);
  }

  static NumberFormat _formatter(BuildContext context,
          [int decimalDigits = 0]) =>
      NumberFormat.currency(
          decimalDigits: decimalDigits,
          locale: Localizations.localeOf(context).toString(),
          name: "");

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final NumberFormat formatter = NumberFormat.currency(
        decimalDigits: decimalDigits,
        locale: Localizations.localeOf(context).toString(),
        name: "");

    return AutoSizeText.rich(
      TextSpan(
        text: "",
        children: [
          TextSpan(
            text: 'N',
            style: style ?? textTheme.bodyLarge,
          ),
          TextSpan(
            text: formatter.format(amount ?? 0),
            style: style ?? textTheme.bodyLarge,
          )
        ],
      ),
      maxLines: 1,
      minFontSize: 10,
    );
  }
}
