import 'package:flutter/material.dart';

class SnackBarHelper {
  // Show error snackbar
  static void showErrorSnackBar(BuildContext context, String message,
      {String title = "Error"}) {
    final screenWidth = MediaQuery.of(context).size.width;
    final margin = screenWidth >= 300
        ? const EdgeInsets.symmetric(horizontal: 300)
        : EdgeInsets.zero;

    _showSnackBar(
      context,
      title: title,
      message: message,
      backgroundColor: Colors.red,
      icon: const Icon(Icons.error, color: Colors.white),
      margin: margin,
    );
  }

  // Show success snackbar
  static void showSuccessSnackBar(BuildContext context, String message,
      {String title = "Success"}) {
    final screenWidth = MediaQuery.of(context).size.width;
    final margin = screenWidth >= 300
        ? const EdgeInsets.symmetric(horizontal: 300)
        : EdgeInsets.zero;

    _showSnackBar(
      context,
      title: title,
      message: message,
      backgroundColor: Colors.green,
      icon: const Icon(Icons.check_circle, color: Colors.white),
      margin: margin,
    );
  }

  // Private helper method to show the snackbar
  static void _showSnackBar(
    BuildContext context, {
    required String title,
    required String message,
    required Color backgroundColor,
    required Icon icon,
    EdgeInsetsGeometry? margin,
  }) {
    final snackBar = SnackBar(
      content: Row(
        children: [
          icon,
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  message,
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: backgroundColor,
      behavior: SnackBarBehavior.floating,
      margin: margin,
      duration: const Duration(seconds: 3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
    );

    // Show the snackbar
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}
