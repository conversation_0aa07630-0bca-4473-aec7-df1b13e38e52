import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:wc_form_validators/wc_form_validators.dart';

enum TextFieldType { text, email, phone, others, password }

class LuxTextField extends StatefulWidget {
  final TextFieldType textFieldType;
  final String? title;
  final String? hint;
  final bool? readonly;
  final bool? disabled;
  final bool? autofocus;
  final TextEditingController? textFieldController;
  final List<TextInputFormatter>? inputFormatters;
  final List<String? Function(String?)>? validator;
  final VoidCallback? onSubmitted;
  final VoidCallback? onTap;
  final InputDecoration? inputDecoration;
  final Function(String)? onChanged;
  final TextInputType? keyboardType;
  final Widget? bottom;
  final String? initialValue;
  final bool? focusedBorder;
  final AutovalidateMode? autovalidateMode;
  final bool? isRequired;
  final int? maxLines;
  final TextCapitalization? textCapitalization;
  final FocusNode? focusNode;
  final bool? obscureText;
  final bool? filled;
  final Color? filledColor;
  final Widget? suffixIcon;
  final TextDirection? textDirection;
  final TextInputAction? textInputAction;
  final String? errorText;

  const LuxTextField({
    super.key,
    required this.textFieldType,
    this.title,
    this.hint,
    this.readonly = false,
    this.disabled = false,
    this.autofocus = false,
    this.textFieldController,
    this.inputFormatters = const [],
    this.validator = const [],
    this.onSubmitted,
    this.onTap,
    this.inputDecoration,
    this.onChanged,
    this.keyboardType,
    this.bottom,
    this.focusedBorder = false,
    this.autovalidateMode,
    this.initialValue,
    this.isRequired = false,
    this.maxLines = 1,
    this.textCapitalization = TextCapitalization.none,
    this.focusNode,
    this.obscureText = false,
    this.filled,
    this.filledColor,
    this.suffixIcon,
    this.textDirection,
    this.textInputAction,
    this.errorText,
  });

  @override
  State<LuxTextField> createState() => _LuxTextFieldState();
}

class _LuxTextFieldState extends State<LuxTextField> {
  final passwordVisible = ValueNotifier<bool>(false);

  final isRequired = Validators.required('Required');
  final textMinLength = Validators.minLength(1, 'Must have a valid entry');
  final email = Validators.email('Enter a valid email');
  final phone = Validators.compose([
    Validators.patternRegExp(
        RegExp(r'^\s*0[789]\d{9}\s*$'), 'Please Enter a Valid Phone Number'),
    Validators.patternRegExp(
      RegExp(
          r'^\s*(?:\+?(\d{1,3}))?[-. (]*(?!0\d{9})(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})(?: *x(\d+))?\s*$'),
      'Please Enter a Valid Phone Number',
    ),
  ]);
  final password = Validators.patternRegExp(
      // RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$'),
      RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$'),
      'Password must be at least 8 characters long, and should include a letter and a number');

  LuxValidators? validators() {
    switch (widget.textFieldType) {
      case TextFieldType.text:
        return widget.isRequired!
            ? Validators.compose(
                [isRequired, textMinLength, ...?widget.validator])
            : Validators.compose([...?widget.validator]);
      case TextFieldType.email:
        return widget.isRequired!
            ? Validators.compose([isRequired, email, ...?widget.validator])
            : Validators.compose([email, ...?widget.validator]);
      case TextFieldType.others:
        return widget.isRequired!
            ? Validators.compose([isRequired, ...?widget.validator])
            : Validators.compose([...?widget.validator]);
      case TextFieldType.phone:
        return widget.isRequired!
            ? Validators.compose([isRequired, phone, ...?widget.validator])
            : Validators.compose([]);
      case TextFieldType.password:
        return Validators.compose([isRequired, password, ...?widget.validator]);
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        if (widget.title != null) ...[
          Skeleton.keep(
            child: Text(
              widget.title!,
            ),
          ),
          const YMargin(6),
        ],
        TextFormField(
          autocorrect: false,
          enableSuggestions: false,
          focusNode: widget.focusNode,
          textCapitalization: widget.textCapitalization!,
          maxLines: widget.maxLines,
          initialValue: widget.initialValue,
          controller: widget.textFieldController,
          autovalidateMode:
              widget.autovalidateMode ?? AutovalidateMode.onUserInteraction,
          validator: validators(),
          obscureText: widget.obscureText!,
          readOnly: widget.readonly!,
          autofocus: widget.autofocus!,
          keyboardType: widget.keyboardType ?? TextInputType.text,
          textDirection: widget.textDirection ?? TextDirection.ltr,
          style: textTheme.bodyLarge?.copyWith(
            decoration: TextDecoration.none,
          ),
          onTap: widget.onTap,
          onChanged: (String value) {
            if (widget.onChanged != null) {
              widget.onChanged!(value);
            }

            setState(() {
              // rebuild
            });
          },
          decoration: widget.inputDecoration ??
              InputDecoration(
                errorMaxLines: 3,
                errorText: widget.errorText,
                hintText: widget.hint,
                hintStyle: textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withValues(alpha: 128)),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 14,
                ),
                enabledBorder: widget.focusedBorder!
                    ? getFocusedFormBorder(context)
                    : getFormBorder(context),
                focusedBorder: getFocusedFormBorder(context),
                border: widget.focusedBorder!
                    ? getFocusedFormBorder(context)
                    : getFormBorder(context),
                filled: widget.filled ??
                    widget.textFieldController?.text.isNotEmpty,
                fillColor: widget.disabled!
                    ? widget.filledColor?.withValues(alpha: 128) ??
                        Theme.of(context)
                            .colorScheme
                            .surface
                            .withValues(alpha: 128)
                    : widget.filledColor ??
                        Theme.of(context).colorScheme.surface,
                suffixIcon: widget.suffixIcon,
              ),
          onEditingComplete: widget.onSubmitted,
          inputFormatters: [...?widget.inputFormatters],
        ),
        const SizedBox(height: 16)
      ],
    );
  }
}
