import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/theme/lux_theme.dart';
import 'package:flutter/material.dart';

class LuxButton extends StatelessWidget {
  const LuxButton({
    super.key,
    required this.onTap,
    required this.text,
    this.disabled = false,

    /// default to 360
    this.width,

    /// default to 56
    this.height,
    this.icon,
  });

  final VoidCallback onTap;
  final String text;
  final bool? disabled;

  /// default to 360
  final double? width;

  /// default to 56
  final double? height;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ConstrainedBox(
      constraints: BoxConstraints.tight(Size(width ?? 360, height ?? 56)),
      child: ElevatedButton(
        onPressed: disabled! ? null : onTap,
        style: Theme.of(context).brightness == Brightness.light
            ? ElevatedButton.styleFrom(
                backgroundColor: LuxTheme.lightSecondary,
                foregroundColor: LuxTheme.lightOnSecondary,
                disabledBackgroundColor:
                    LuxTheme.lightSecondary.withAlpha(102), // 0.4 opacity
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50),
                ),
              )
            : ElevatedButton.styleFrom(
                backgroundColor: LuxTheme.darkSecondary,
                foregroundColor: LuxTheme.darkOnSecondary,
                disabledBackgroundColor:
                    LuxTheme.darkSecondary.withAlpha(102), // 0.4 opacity
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50),
                ),
              ),
        // Theme.of(context).brightness == Brightness.light
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              icon!,
              const XMargin(6),
            ],
            Text(
              text,
              style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary),
            ),
          ],
        ),
      ),
    );
  }

  static Widget outlined(
          {Key? key,
          String? text,
          required VoidCallback onTap,
          bool? disabled = false,
          BoxConstraints? constraints,
          Widget? icon}) =>
      ConstrainedBox(
        constraints: constraints ??
            const BoxConstraints.tightFor(width: 300, height: 56),
        child: Builder(
          builder: (context) {
            final theme = Theme.of(context);
            final colorScheme = theme.colorScheme;

            return OutlinedButton(
              onPressed: disabled! ? null : onTap,
              style: Theme.of(context).brightness == Brightness.light
                  ? OutlinedButton.styleFrom(
                      foregroundColor: LuxTheme.lightSecondary,
                      side: const BorderSide(color: LuxTheme.lightSecondary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                    )
                  : OutlinedButton.styleFrom(
                      foregroundColor: LuxTheme.darkSecondary,
                      side: const BorderSide(color: LuxTheme.darkSecondary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                    ),
              child: Row(
                children: [
                  if (icon != null) ...[
                    icon,
                    const XMargin(6),
                  ],
                  Text(
                    text ?? '',
                    style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.secondary.withValues(
                            alpha: disabled ? 0.4 : 1) // 0.4 opacity

                        ),
                  ),
                ],
              ),
            );
          },
        ),
      );
}
