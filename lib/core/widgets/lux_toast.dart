import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';

class Toast {
  // Get theme-aware colors
  static Color _getBackgroundColor(BuildContext? context) {
    if (context != null) {
      return Theme.of(context).colorScheme.surface;
    }
    return const Color(0xFFEEEEEE); // Fallback light grey
  }

  static Color _getContentColor(BuildContext? context) {
    if (context != null) {
      return Theme.of(context).colorScheme.primary;
    }
    return const Color(0xff040302); // Fallback dark color
  }

  static void showText(
    String text, {
    int? seconds,
    BuildContext? context,
  }) {
    final Color contentColor = _getContentColor(context);

    BotToast.showText(
      text: text,
      contentColor: contentColor,
      duration: Duration(seconds: seconds ?? 5),
      textStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        fontFamily: 'AvenirNextCyr',
      ),
    );
  }

  static void success(
    String message, {
    String? title,
    int? seconds,
    BuildContext? context,
  }) {
    const Color successColor = Color(0xFF25D366); // Green
    final Color backgroundColor = _getBackgroundColor(context);

    BotToast.showNotification(
        leading: (_) => const Icon(
              Icons.check_circle_outline,
              color: successColor,
              size: 32.0,
            ),
        title: (_) => Text(
              title ?? 'Success',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'AvenirNextCyr',
                color: successColor,
              ),
            ),
        subtitle: (_) => Text(
              message,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'AvenirNextCyr',
                color: successColor,
              ),
            ),
        onTap: () {
          Toast.close();
        },
        duration:
            Duration(milliseconds: seconds != null ? (seconds * 1000) : 5000),
        backgroundColor: backgroundColor);
  }

  static void error(
    String message, {
    String? title,
    int? seconds,
    BuildContext? context,
  }) {
    const Color errorColor = Color(0xFFEB0000); // Red
    final Color backgroundColor = _getBackgroundColor(context);

    BotToast.showNotification(
        leading: (_) => const Icon(
              Icons.error_outline,
              color: errorColor,
              size: 32.0,
            ),
        title: (_) => Text(
              title ?? 'Error',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'AvenirNextCyr',
                color: errorColor,
              ),
            ),
        subtitle: (_) => Text(
              message,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w800,
                fontFamily: 'AvenirNextCyr',
                color: errorColor,
              ),
            ),
        onTap: () {
          Toast.close();
        },
        duration:
            Duration(milliseconds: seconds != null ? (seconds * 1000) : 5000),
        backgroundColor: backgroundColor);
  }

  static void info(
    String message, {
    String? title,
    int? seconds,
    BuildContext? context,
  }) {
    const Color infoColor = Color(0xFFF59300); // Orange
    final Color backgroundColor = _getBackgroundColor(context);

    BotToast.showNotification(
        leading: (_) => const Icon(
              Icons.warning_outlined,
              color: infoColor,
              size: 32.0,
            ),
        title: (_) => Text(
              title ?? 'Info',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w900,
                fontFamily: 'AvenirNextCyr',
                color: infoColor,
              ),
            ),
        subtitle: (_) => Text(
              message,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w800,
                fontFamily: 'AvenirNextCyr',
                color: infoColor,
              ),
            ),
        onTap: () {
          Toast.close();
        },
        duration:
            Duration(milliseconds: seconds != null ? (seconds * 1000) : 5000),
        backgroundColor: backgroundColor);
  }

  static void close() {
    BotToast.cleanAll();
  }
}
