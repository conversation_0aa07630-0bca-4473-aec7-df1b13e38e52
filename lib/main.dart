import 'package:bot_toast/bot_toast.dart';
import 'package:cote_network_logger/cote_network_logger.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:fleet_mobile/firebase_config/firebase_options_dev.dart' as dev;
import 'package:fleet_mobile/firebase_config/firebase_options_prod.dart'
    as prod;
import 'package:fleet_mobile/theme/lux_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'l10n/l10.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

   await startNetworkLogServer();

  FirebaseOptions? firebaseOptions = config.environment == ENVIRONMENT.dev
      ? dev.DefaultFirebaseOptions.currentPlatform
      : prod.DefaultFirebaseOptions.currentPlatform;
  await Firebase.initializeApp(name: firebaseAppName, options: firebaseOptions);

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appRouter = ref.watch(routeProvider);

    return GestureDetector(
      onTap: () {
        // un-focus input on tap out
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
          currentFocus.requestFocus(FocusNode());
        }
      },
      child: MaterialApp.router(
        title: 'LuxLet-FLeet',
        debugShowCheckedModeBanner: false,
        theme: LuxTheme.darkTheme(context),
        // theme: LuxTheme.lightTheme(context),
        // darkTheme: LuxTheme.darkTheme(context),
        supportedLocales: L10n.all,
        themeMode: ThemeMode.system, // Automatically use the system theme
        routerConfig: appRouter.router,
        builder: (context, child) {
          return BotToastInit()(context, child);
        },
      ),
    );
  }
}
