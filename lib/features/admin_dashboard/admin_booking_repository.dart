import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final adminBookingRepositoryProvider = Provider<AdminBookingRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return LuxAdminBookingRepository(dio);
});

abstract class AdminBookingRepository {
  Future<List<Booking>> getRejectedBookings();
  Future<BookingMeta> getBookingMeta();
}

class LuxAdminBookingRepository implements AdminBookingRepository {
  LuxAdminBookingRepository(this.dio);

  final Dio dio;

  @override
  Future<List<Booking>> getRejectedBookings() async {
    return await dioInterceptor(() async {
      final res = await dio.get('bookings/rejected');
      final List<dynamic> bookings = res.data['data'];
      return bookings.map((x) => Booking.fromMap(x)).toList();
    });
  }

  @override
  Future<BookingMeta> getBookingMeta() async {
    return await dioInterceptor(() async {
      final res = await dio.get('bookings/meta');
      return BookingMeta.fromMap(res.data['data']);
    });
  }
}
