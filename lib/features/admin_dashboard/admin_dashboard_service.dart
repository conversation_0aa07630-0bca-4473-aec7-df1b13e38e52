import 'package:fleet_mobile/core/core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'admin_dashboard_repository.dart';

final adminDashboardServiceProvider = Provider<AdminDashboardService>(
  (ref) {
    final repository = ref.read(adminDashboardRepositoryProvider);
    return LuxAdminDashboardService(repository);
  },
);

abstract class AdminDashboardService {
  Future<Result<List<User>, Failure>> getAdmins();
  Future<Result<List<User>, Failure>> getUsers();
  Future<Result<List<User>, Failure>> getSuperAdmins();
  Future<Result<List<Car>, Failure>> getUserCars(String userId);
  Future<Result<Unit, Failure>> createAdvert(
      String carId, DateTime startDate, DateTime endDate);
  Future<Result<List<Advert>, Failure>> getAdverts();
  Future<Result<Unit, Failure>> deleteAdvert(String advertId);
  Future<Result<Unit, Failure>> updateUserRole(String userId, String role);
}

class LuxAdminDashboardService implements AdminDashboardService {
  LuxAdminDashboardService(this._repo);

  final AdminDashboardRepository _repo;

  @override
  Future<Result<List<User>, Failure>> getAdmins() async {
    try {
      final res = await _repo.getAdmins();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<User>, Failure>> getUsers() async {
    try {
      final res = await _repo.getUsers();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<User>, Failure>> getSuperAdmins() async {
    try {
      final res = await _repo.getSuperAdmins();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<Car>, Failure>> getUserCars(String userId) async {
    try {
      final res = await _repo.getUserCars(userId);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> createAdvert(
      String carId, DateTime startDate, DateTime endDate) async {
    try {
      final res = await _repo.createAdvert(carId, startDate, endDate);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<Advert>, Failure>> getAdverts() async {
    try {
      final res = await _repo.getAdverts();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> deleteAdvert(String advertId) async {
    try {
      final res = await _repo.deleteAdvert(advertId);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> updateUserRole(
      String userId, String role) async {
    try {
      final res = await _repo.updateUserRole(userId, role);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }
}
