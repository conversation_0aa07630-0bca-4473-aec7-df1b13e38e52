import 'package:fleet_mobile/core/core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'admin_dashboard_repository.dart';

final adminDashboardServiceProvider = Provider<AdminDashboardService>(
  (ref) {
    final repository = ref.read(adminDashboardRepositoryProvider);
    return LuxAdminDashboardService(repository);
  },
);

abstract class AdminDashboardService {
  // User management
  Future<Result<List<User>, Failure>> getAdmins();
  Future<Result<List<User>, Failure>> getUsers();
  Future<Result<List<User>, Failure>> getSuperAdmins();
  Future<Result<List<Car>, Failure>> getUserCars(String userId);
  Future<Result<Unit, Failure>> updateUserRole(String userId, String role);

  // Advert management
  Future<Result<Unit, Failure>> createAdvert(
      String carId, DateTime startDate, DateTime endDate);
  Future<Result<List<Advert>, Failure>> getAdverts();
  Future<Result<Unit, Failure>> deleteAdvert(String advertId);

  // Booking management
  Future<Result<List<Booking>, Failure>> getRejectedBookings();
  Future<Result<BookingMeta, Failure>> getBookingMeta();

  // Brand and Category management
  Future<Result<List<Brand>, Failure>> getBrands();
  Future<Result<Brand, Failure>> createBrand(String name);
  Future<Result<Brand, Failure>> updateBrand(String id, String name);
  Future<Result<Unit, Failure>> deleteBrand(String id);
  Future<Result<List<Category>, Failure>> getCategories();
  Future<Result<Category, Failure>> createCategory(
      String name, String imagePath);
  Future<Result<Category, Failure>> updateCategory(String id, String name);
  Future<Result<Unit, Failure>> deleteCategory(String id);
}

class LuxAdminDashboardService implements AdminDashboardService {
  LuxAdminDashboardService(this._repo);

  final AdminDashboardRepository _repo;

  @override
  Future<Result<List<User>, Failure>> getAdmins() async {
    try {
      final res = await _repo.getAdmins();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<User>, Failure>> getUsers() async {
    try {
      final res = await _repo.getUsers();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<User>, Failure>> getSuperAdmins() async {
    try {
      final res = await _repo.getSuperAdmins();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<Car>, Failure>> getUserCars(String userId) async {
    try {
      final res = await _repo.getUserCars(userId);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> createAdvert(
      String carId, DateTime startDate, DateTime endDate) async {
    try {
      final res = await _repo.createAdvert(carId, startDate, endDate);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<Advert>, Failure>> getAdverts() async {
    try {
      final res = await _repo.getAdverts();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> deleteAdvert(String advertId) async {
    try {
      final res = await _repo.deleteAdvert(advertId);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> updateUserRole(
      String userId, String role) async {
    try {
      final res = await _repo.updateUserRole(userId, role);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  // Booking management implementations
  @override
  Future<Result<List<Booking>, Failure>> getRejectedBookings() async {
    try {
      final res = await _repo.getRejectedBookings();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<BookingMeta, Failure>> getBookingMeta() async {
    try {
      final res = await _repo.getBookingMeta();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  // Brand management implementations
  @override
  Future<Result<List<Brand>, Failure>> getBrands() async {
    try {
      final res = await _repo.getBrands();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Brand, Failure>> createBrand(String name) async {
    try {
      final res = await _repo.createBrand(name);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Brand, Failure>> updateBrand(String id, String name) async {
    try {
      final res = await _repo.updateBrand(id, name);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> deleteBrand(String id) async {
    try {
      await _repo.deleteBrand(id);
      return const Success(unit);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  // Category management implementations
  @override
  Future<Result<List<Category>, Failure>> getCategories() async {
    try {
      final res = await _repo.getCategories();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Category, Failure>> createCategory(
      String name, String imagePath) async {
    try {
      final res = await _repo.createCategory(name, imagePath);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Category, Failure>> updateCategory(
      String id, String name) async {
    try {
      final res = await _repo.updateCategory(id, name);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> deleteCategory(String id) async {
    try {
      await _repo.deleteCategory(id);
      return const Success(unit);
    } on Failure catch (e) {
      return Error(e);
    }
  }
}
