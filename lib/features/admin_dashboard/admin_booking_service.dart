import 'package:fleet_mobile/core/core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'admin_booking_repository.dart';

final adminBookingServiceProvider = Provider<AdminBookingService>((ref) {
  final repository = ref.read(adminBookingRepositoryProvider);
  return LuxAdminBookingService(repository);
});

abstract class AdminBookingService {
  Future<Result<List<Booking>, Failure>> getRejectedBookings();
  Future<Result<BookingMeta, Failure>> getBookingMeta();
}

class LuxAdminBookingService implements AdminBookingService {
  final AdminBookingRepository _repository;

  LuxAdminBookingService(this._repository);

  @override
  Future<Result<List<Booking>, Failure>> getRejectedBookings() async {
    try {
      final res = await _repository.getRejectedBookings();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<BookingMeta, Failure>> getBookingMeta() async {
    try {
      final res = await _repository.getBookingMeta();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }
}
