import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

final adminDashboardRepositoryProvider = Provider<AdminDashboardRepository>(
  (ref) {
    final dio = ref.watch(dioProvider);
    return LuxAdminDashboardRepository(dio);
  },
);

abstract class AdminDashboardRepository {
  Future<List<User>> getAdmins();
  Future<List<User>> getUsers();
  Future<List<User>> getSuperAdmins();
  Future<List<Car>> getUserCars(String userId);
  Future<Unit> createAdvert(String carId, DateTime startDate, DateTime endDate);
  Future<List<Advert>> getAdverts();
  Future<Unit> deleteAdvert(String advertId);
  Future<Unit> updateUserRole(String userId, String role);
}

class LuxAdminDashboardRepository implements AdminDashboardRepository {
  final Dio dio;

  LuxAdminDashboardRepository(this.dio);

  @override
  Future<List<User>> getAdmins() async {
    return await dioInterceptor(() async {
      final res = await dio.get('users/admins');
      final List<dynamic> users = res.data['data'];
      return users.map((x) => User.fromMap(x)).toList();
    });
  }

  @override
  Future<List<User>> getUsers() async {
    return await dioInterceptor(() async {
      final res = await dio.get('users');
      final List<dynamic> users = res.data['data'];
      return users.map((x) => User.fromMap(x)).toList();
    });
  }

  @override
  Future<List<User>> getSuperAdmins() async {
    return await dioInterceptor(() async {
      final res = await dio.get('users/super-admins');
      final List<dynamic> users = res.data['data'];
      return users.map((x) => User.fromMap(x)).toList();
    });
  }

  @override
  Future<List<Car>> getUserCars(String userId) async {
    return await dioInterceptor(() async {
      final res = await dio.get('users/$userId/cars');
      final List<dynamic> cars = res.data['data'];
      return cars.map((x) => Car.fromMap(x)).toList();
    });
  }

  @override
  Future<Unit> createAdvert(
      String carId, DateTime startDate, DateTime endDate) async {
    return await dioInterceptor(() async {
      await dio.post('adverts/create', data: {
        'carId': carId,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
      });
      return unit;
    });
  }

  @override
  Future<List<Advert>> getAdverts() async {
    return await dioInterceptor(() async {
      final res = await dio.get('adverts');
      final List<dynamic> adverts = res.data['data'];
      return adverts.map((x) => Advert.fromMap(x)).toList();
    });
  }

  @override
  Future<Unit> deleteAdvert(String advertId) async {
    return await dioInterceptor(() async {
      await dio.delete('adverts/$advertId');
      return unit;
    });
  }

  @override
  Future<Unit> updateUserRole(String userId, String role) async {
    return await dioInterceptor(() async {
      await dio.patch('users/$userId', data: {'role': role});
      return unit;
    });
  }
}
