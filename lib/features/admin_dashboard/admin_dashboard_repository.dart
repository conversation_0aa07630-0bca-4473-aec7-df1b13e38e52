import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

final adminDashboardRepositoryProvider = Provider<AdminDashboardRepository>(
  (ref) {
    final dio = ref.watch(dioProvider);
    return LuxAdminDashboardRepository(dio);
  },
);

abstract class AdminDashboardRepository {
  // User management
  Future<List<User>> getAdmins();
  Future<List<User>> getUsers();
  Future<List<User>> getSuperAdmins();
  Future<List<Car>> getUserCars(String userId);
  Future<Unit> updateUserRole(String userId, String role);

  // Advert management
  Future<Unit> createAdvert(String carId, DateTime startDate, DateTime endDate);
  Future<List<Advert>> getAdverts();
  Future<Unit> deleteAdvert(String advertId);

  // Booking management
  Future<List<Booking>> getRejectedBookings();
  Future<BookingMeta> getBookingMeta();

  // Brand and Category management
  Future<List<Brand>> getBrands();
  Future<Brand> createBrand(String name);
  Future<Brand> updateBrand(String id, String name);
  Future<void> deleteBrand(String id);
  Future<List<Category>> getCategories();
  Future<Category> createCategory(String name, String imagePath);
  Future<Category> updateCategory(String id, String name);
  Future<void> deleteCategory(String id);
}

class LuxAdminDashboardRepository implements AdminDashboardRepository {
  final Dio dio;

  LuxAdminDashboardRepository(this.dio);

  @override
  Future<List<User>> getAdmins() async {
    return await dioInterceptor(() async {
      final res = await dio.get('users/admins');
      final List<dynamic> users = res.data['data'];
      return users.map((x) => User.fromMap(x)).toList();
    });
  }

  @override
  Future<List<User>> getUsers() async {
    return await dioInterceptor(() async {
      final res = await dio.get('users');
      final List<dynamic> users = res.data['data'];
      return users.map((x) => User.fromMap(x)).toList();
    });
  }

  @override
  Future<List<User>> getSuperAdmins() async {
    return await dioInterceptor(() async {
      final res = await dio.get('users/super-admins');
      final List<dynamic> users = res.data['data'];
      return users.map((x) => User.fromMap(x)).toList();
    });
  }

  @override
  Future<List<Car>> getUserCars(String userId) async {
    return await dioInterceptor(() async {
      final res = await dio.get('users/$userId/cars');
      final List<dynamic> cars = res.data['data'];
      return cars.map((x) => Car.fromMap(x)).toList();
    });
  }

  @override
  Future<Unit> createAdvert(
      String carId, DateTime startDate, DateTime endDate) async {
    return await dioInterceptor(() async {
      await dio.post('adverts/create', data: {
        'carId': carId,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
      });
      return unit;
    });
  }

  @override
  Future<List<Advert>> getAdverts() async {
    return await dioInterceptor(() async {
      final res = await dio.get('adverts');
      final List<dynamic> adverts = res.data['data'];
      return adverts.map((x) => Advert.fromMap(x)).toList();
    });
  }

  @override
  Future<Unit> deleteAdvert(String advertId) async {
    return await dioInterceptor(() async {
      await dio.delete('adverts/$advertId');
      return unit;
    });
  }

  @override
  Future<Unit> updateUserRole(String userId, String role) async {
    return await dioInterceptor(() async {
      await dio.patch('users/$userId', data: {'role': role});
      return unit;
    });
  }

  // Booking management implementations
  @override
  Future<List<Booking>> getRejectedBookings() async {
    return await dioInterceptor(() async {
      final res = await dio.get('bookings/rejected');
      final List<dynamic> bookings = res.data['data'];
      return bookings.map((x) => Booking.fromMap(x)).toList();
    });
  }

  @override
  Future<BookingMeta> getBookingMeta() async {
    return await dioInterceptor(() async {
      final res = await dio.get('bookings/meta');
      return BookingMeta.fromMap(res.data['data']);
    });
  }

  // Brand management implementations
  @override
  Future<List<Brand>> getBrands() async {
    return await dioInterceptor(() async {
      final res = await dio.get('brands');
      final List<dynamic> brands = res.data['data'];
      return brands.map((x) => Brand.fromMap(x)).toList();
    });
  }

  @override
  Future<Brand> createBrand(String name) async {
    return await dioInterceptor(() async {
      final res = await dio.post('brands', data: {'name': name});
      return Brand.fromMap(res.data['data']);
    });
  }

  @override
  Future<Brand> updateBrand(String id, String name) async {
    return await dioInterceptor(() async {
      final res = await dio.put('brands/$id', data: {'name': name});
      return Brand.fromMap(res.data['data']);
    });
  }

  @override
  Future<void> deleteBrand(String id) async {
    return await dioInterceptor(() async {
      await dio.delete('brands/$id');
    });
  }

  // Category management implementations
  @override
  Future<List<Category>> getCategories() async {
    return await dioInterceptor(() async {
      final res = await dio.get('cars/category');
      final List<dynamic> categories = res.data['data'];
      return categories.map((x) => Category.fromMap(x)).toList();
    });
  }

  @override
  Future<Category> createCategory(String name, String imagePath) async {
    return await dioInterceptor(() async {
      final formData = FormData.fromMap({
        'name': name,
        'image': await MultipartFile.fromFile(imagePath),
      });

      final res = await dio.post('cars/category', data: formData);
      return Category.fromMap(res.data['data']);
    });
  }

  @override
  Future<Category> updateCategory(String id, String name) async {
    return await dioInterceptor(() async {
      final res = await dio.put('cars/category/$id', data: {'name': name});
      return Category.fromMap(res.data['data']);
    });
  }

  @override
  Future<void> deleteCategory(String id) async {
    return await dioInterceptor(() async {
      await dio.delete('cars/category/$id');
    });
  }
}
