import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/values.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'admin_dashboard_service.dart';
import 'admin_dashboard_state.dart';

final adminDashboardControllerProvider =
    StateNotifierProvider<AdminDashboardController, AdminDashboardState>(
  (ref) {
    final service = ref.read(adminDashboardServiceProvider);
    return AdminDashboardController(AdminDashboardState.initial(), service);
  },
);

class AdminDashboardController extends StateNotifier<AdminDashboardState> {
  AdminDashboardController(super.state, this._service);

  final AdminDashboardService _service;

  Future<void> getAdmins([int retry = 3]) async {
    state = state.copyWith(admins: const AsyncValue.loading());
    final res = await _service.getAdmins();
    res.when(
      (users) {
        state = state.copyWith(admins: AsyncValue.data(users));
      },
      (error) async {
        if (retry > 0) {
          await Future.delayed(
              const Duration(milliseconds: retryDelayMilliseconds));
          getAdmins(retry - 1);
        } else {
          state = state.copyWith(
            admins: AsyncValue.error(error, StackTrace.current),
          );
        }
      },
    );
  }

  Future<void> getUsers([int retry = 3]) async {
    state = state.copyWith(users: const AsyncValue.loading());
    final res = await _service.getUsers();
    res.when(
      (users) {
        state = state.copyWith(users: AsyncValue.data(users));
      },
      (error) async {
        if (retry > 0) {
          await Future.delayed(
              const Duration(milliseconds: retryDelayMilliseconds));
          getUsers(retry - 1);
        } else {
          state = state.copyWith(
            users: AsyncValue.error(error, StackTrace.current),
          );
        }
      },
    );
  }

  Future<void> getSuperAdmins([int retry = 3]) async {
    state = state.copyWith(superAdmins: const AsyncValue.loading());
    final res = await _service.getSuperAdmins();
    res.when(
      (users) {
        state = state.copyWith(superAdmins: AsyncValue.data(users));
      },
      (error) async {
        if (retry > 0) {
          await Future.delayed(
              const Duration(milliseconds: retryDelayMilliseconds));
          getSuperAdmins(retry - 1);
        } else {
          state = state.copyWith(
            superAdmins: AsyncValue.error(error, StackTrace.current),
          );
        }
      },
    );
  }

  Future<void> getAdverts([int retry = 3]) async {
    state = state.copyWith(adverts: const AsyncValue.loading());
    final res = await _service.getAdverts();
    res.when(
      (adverts) {
        state = state.copyWith(adverts: AsyncValue.data(adverts));
      },
      (error) async {
        if (retry > 0) {
          await Future.delayed(
              const Duration(milliseconds: retryDelayMilliseconds));
          getAdverts(retry - 1);
        } else {
          state = state.copyWith(
            adverts: AsyncValue.error(error, StackTrace.current),
          );
        }
      },
    );
  }
}
