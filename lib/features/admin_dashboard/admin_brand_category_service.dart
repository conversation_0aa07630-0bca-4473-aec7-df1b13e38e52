import 'package:fleet_mobile/core/core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'admin_brand_category_repository.dart';

final adminBrandCategoryServiceProvider =
    Provider<AdminBrandCategoryService>((ref) {
  final repository = ref.read(adminBrandCategoryRepositoryProvider);
  return LuxAdminBrandCategoryService(repository);
});

abstract class AdminBrandCategoryService {
  // Brand operations
  Future<Result<List<Brand>, Failure>> getBrands();
  Future<Result<Brand, Failure>> createBrand(String name);
  Future<Result<Brand, Failure>> updateBrand(String id, String name);
  Future<Result<void, Failure>> deleteBrand(String id);

  // Category operations
  Future<Result<List<Category>, Failure>> getCategories();
  Future<Result<Category, Failure>> createCategory(
      String name, String imagePath);
  Future<Result<Category, Failure>> updateCategory(String id, String name);
  Future<Result<void, Failure>> deleteCategory(String id);
}

class LuxAdminBrandCategoryService implements AdminBrandCategoryService {
  final AdminBrandCategoryRepository _repository;

  LuxAdminBrandCategoryService(this._repository);

  @override
  Future<Result<List<Brand>, Failure>> getBrands() async {
    try {
      final res = await _repository.getBrands();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Brand, Failure>> createBrand(String name) async {
    try {
      final res = await _repository.createBrand(name);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Brand, Failure>> updateBrand(String id, String name) async {
    try {
      final res = await _repository.updateBrand(id, name);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<void, Failure>> deleteBrand(String id) async {
    try {
      await _repository.deleteBrand(id);
      return const Success(null);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<Category>, Failure>> getCategories() async {
    try {
      final res = await _repository.getCategories();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Category, Failure>> createCategory(
      String name, String imagePath) async {
    try {
      final res = await _repository.createCategory(name, imagePath);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Category, Failure>> updateCategory(
      String id, String name) async {
    try {
      final res = await _repository.updateCategory(id, name);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<void, Failure>> deleteCategory(String id) async {
    try {
      await _repository.deleteCategory(id);
      return const Success(null);
    } on Failure catch (e) {
      return Error(e);
    }
  }
}
