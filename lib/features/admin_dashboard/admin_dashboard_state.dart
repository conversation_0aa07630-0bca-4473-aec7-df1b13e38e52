import 'package:equatable/equatable.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

@immutable
class AdminDashboardState extends Equatable {
  final AsyncValue<List<User>> users;
  final AsyncValue<List<User>> admins;
  final AsyncValue<List<User>> superAdmins;
  final AsyncValue<List<Advert>> adverts;

  const AdminDashboardState({
    this.users = const AsyncValue.data([]),
    this.admins = const AsyncValue.data([]),
    this.superAdmins = const AsyncValue.data([]),
    this.adverts = const AsyncValue.data([]),
  });

  factory AdminDashboardState.initial() {
    return const AdminDashboardState();
  }

  AdminDashboardState copyWith({
    AsyncValue<List<User>>? users,
    AsyncValue<List<User>>? admins,
    AsyncValue<List<User>>? superAdmins,
    AsyncValue<List<Advert>>? adverts,
  }) {
    return AdminDashboardState(
      users: users ?? this.users,
      admins: admins ?? this.admins,
      superAdmins: superAdmins ?? this.superAdmins,
      adverts: adverts ?? this.adverts,
    );
  }

  @override
  List<Object?> get props => [
        users,
        admins,
        superAdmins,
        adverts,
      ];
}
