import 'dart:io';

import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_brand_category_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:wc_form_validators/wc_form_validators.dart';

class CategoryManagementScreen extends ConsumerStatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  ConsumerState<CategoryManagementScreen> createState() =>
      _CategoryManagementScreenState();
}

class _CategoryManagementScreenState
    extends ConsumerState<CategoryManagementScreen> {
  AsyncValue<List<Category>> _categories = const AsyncData([]);
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _fetchCategories();
  }

  Future<void> _fetchCategories() async {
    setState(() {
      _categories = const AsyncLoading();
    });

    final res =
        await ref.read(adminBrandCategoryServiceProvider).getCategories();
    res.when(
      (categories) {
        setState(() {
          _categories = AsyncData(categories);
        });
      },
      (e) {
        setState(() {
          _categories = AsyncError(e, StackTrace.current);
        });
      },
    );
  }

  Future<void> _showCreateCategoryDialog() async {
    final nameController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    XFile? selectedImage;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Create Category'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                LuxTextField(
                  textFieldType: TextFieldType.text,
                  title: 'Category Name',
                  hint: 'Enter category name',
                  textFieldController: nameController,
                  isRequired: true,
                  validator: [Validators.required('Category name is required')],
                ),
                const YMargin(16),
                if (selectedImage != null) ...[
                  Container(
                    height: 100,
                    width: 100,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: FileImage(File(selectedImage!.path)),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const YMargin(8),
                ],
                ElevatedButton.icon(
                  onPressed: () async {
                    final image =
                        await _picker.pickImage(source: ImageSource.gallery);
                    if (image != null) {
                      setDialogState(() {
                        selectedImage = image;
                      });
                    }
                  },
                  icon: const Icon(Icons.image),
                  label: Text(
                      selectedImage == null ? 'Select Image' : 'Change Image'),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate() && selectedImage != null) {
                  Navigator.pop(context, true);
                } else if (selectedImage == null) {
                  Toast.error('Please select an image');
                }
              },
              child: const Text('Create'),
            ),
          ],
        ),
      ),
    );

    if (result == true &&
        nameController.text.isNotEmpty &&
        selectedImage != null) {
      Loader.show(context);
      final res = await ref
          .read(adminBrandCategoryServiceProvider)
          .createCategory(nameController.text.trim(), selectedImage!.path);

      res.when(
        (category) {
          Loader.hide();
          Toast.success('Category created successfully');
          _fetchCategories();
        },
        (error) {
          Loader.hide();
          Toast.error(error.message);
        },
      );
    }
  }

  Future<void> _showEditCategoryDialog(Category category) async {
    final nameController = TextEditingController(text: category.name);
    final formKey = GlobalKey<FormState>();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Category'),
        content: Form(
          key: formKey,
          child: LuxTextField(
            textFieldType: TextFieldType.text,
            title: 'Category Name',
            hint: 'Enter category name',
            textFieldController: nameController,
            isRequired: true,
            validator: [Validators.required('Category name is required')],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context, true);
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );

    if (result == true && nameController.text.isNotEmpty) {
      Loader.show(context);
      final res = await ref
          .read(adminBrandCategoryServiceProvider)
          .updateCategory(category.id, nameController.text.trim());

      res.when(
        (updatedCategory) {
          Loader.hide();
          Toast.success('Category updated successfully');
          _fetchCategories();
        },
        (error) {
          Loader.hide();
          Toast.error(error.message);
        },
      );
    }
  }

  Future<void> _deleteCategory(Category category) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (result == true) {
      Loader.show(context);
      final res = await ref
          .read(adminBrandCategoryServiceProvider)
          .deleteCategory(category.id);

      res.when(
        (_) {
          Loader.hide();
          Toast.success('Category deleted successfully');
          _fetchCategories();
        },
        (error) {
          Loader.hide();
          Toast.error(error.message);
        },
      );
    }
  }

  Widget _buildCategoryList(List<Category> categories) {
    if (categories.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.category, size: 64, color: Colors.grey),
            YMargin(16),
            Text('No categories found'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: const CircleAvatar(
              child: Icon(Icons.category),
            ),
            title: Text(category.name),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditCategoryDialog(category),
                  tooltip: 'Edit Category',
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _deleteCategory(category),
                  tooltip: 'Delete Category',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Category Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showCreateCategoryDialog,
            tooltip: 'Add Category',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _fetchCategories,
        child: _categories.when(
          data: (categories) => _buildCategoryList(categories),
          error: (e, s) => FailureWidget(
            fullScreen: true,
            e: e,
            retry: _fetchCategories,
          ),
          loading: () => Skeletonizer(
            enabled: true,
            child:
                _buildCategoryList(List.filled(5, Category.defaultCategory())),
          ),
        ),
      ),
    );
  }
}
