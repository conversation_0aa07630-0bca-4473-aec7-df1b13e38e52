import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_dashboard_controller.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_dashboard_service.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';

class UserListScreen extends ConsumerStatefulWidget {
  const UserListScreen({super.key});

  @override
  ConsumerState<UserListScreen> createState() => _UserListScreenState();
}

class _UserListScreenState extends ConsumerState<UserListScreen> {
  @override
  void initState() {
    super.initState();
    _fetchUsers();
  }

  void _fetchUsers() {
    Future.microtask(() {
      ref.read(adminDashboardControllerProvider.notifier).getUsers();
    });
  }

  @override
  Widget build(BuildContext context) {
    final adminDashboardProvider = ref.watch(adminDashboardControllerProvider);
    final adminDashboardNotifier =
        ref.watch(adminDashboardControllerProvider.notifier);
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Users'),
      ),
      body: adminDashboardProvider.users.when(
        data: (users) => _buildUserList(users),
        error: (e, s) => FailureWidget(
            fullScreen: true,
            e: e,
            retry: () => adminDashboardNotifier.getUsers()),
        loading: () => Skeletonizer(
          enabled: true,
          child: _buildUserList(List.filled(5, User.defaultValue())),
        ),
      ),
    );
  }

  Widget _buildUserList(List<User> users) {
    if (users.isEmpty) {
      final primaryColorLight = Theme.of(context).primaryColorLight;
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No Users',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: primaryColorLight,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'There are no users in the system yet',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: primaryColorLight,
                  ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: user.isAdmin ? Colors.blue : Colors.grey,
              child: Icon(
                user.isAdmin ? Icons.admin_panel_settings : Icons.person,
                color: Colors.white,
              ),
            ),
            title: Text(user.fullName),
            subtitle: Text(user.email),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.car_rental),
                  onPressed: () =>
                      context.push('/admin/users/${user.id}/cars', extra: user),
                  tooltip: 'View Cars',
                ),
                Consumer(builder: (context, ref, child) {
                  final loggedInUser = ref.watch(authControllerProvider).user;
                  return loggedInUser?.isSuperAdmin == true
                      ? IconButton(
                          icon: Icon(
                            user.isAdmin
                                ? Icons.remove_circle_outline
                                : Icons.add_circle_outline,
                            color: user.isAdmin ? Colors.red : Colors.green,
                          ),
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text(user.isAdmin
                                    ? 'Revoke Admin Access'
                                    : 'Grant Admin Access'),
                                content: Text(
                                  user.isAdmin
                                      ? 'Are you sure you want to revoke admin access for this user?'
                                      : 'Are you sure you want to grant admin access to this user?',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: Text(
                                      'Cancel',
                                      style: TextStyle(
                                          color: Theme.of(context)
                                              .primaryColorLight),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () async {
                                      Navigator.pop(context);

                                      Loader.show(context);

                                      final res = await ref
                                          .read(adminDashboardServiceProvider)
                                          .updateUserRole(
                                              user.id,
                                              user.isAdmin
                                                  ? 'manager'
                                                  : 'admin');

                                      res.when((data) {
                                        _fetchUsers();
                                        Toast.success(
                                            'User role updated successfully');
                                        Loader.hide();
                                      }, (e) {
                                        Toast.error(e.message);
                                        Loader.hide();
                                      });
                                    },
                                    child: Text(
                                        user.isAdmin ? 'Revoke' : 'Grant',
                                        style: TextStyle(
                                            color: user.isAdmin
                                                ? Colors.red
                                                : Colors.green)),
                                  ),
                                ],
                              ),
                            );
                          },
                          tooltip:
                              user.isAdmin ? 'Revoke Admin' : 'Grant Admin',
                        )
                      : const SizedBox.shrink();
                }),
              ],
            ),
          ),
        );
      },
    );
  }
}
