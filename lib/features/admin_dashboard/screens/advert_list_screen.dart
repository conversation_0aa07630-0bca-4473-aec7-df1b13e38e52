import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_dashboard_controller.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_dashboard_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AdvertListScreen extends ConsumerStatefulWidget {
  const AdvertListScreen({super.key});

  @override
  ConsumerState<AdvertListScreen> createState() => _AdvertListScreenState();
}

class _AdvertListScreenState extends ConsumerState<AdvertListScreen> {
  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      ref.read(adminDashboardControllerProvider.notifier).getAdverts();
    });
  }

  String _formatDate(DateTime date) {
    return DateFormat('EEE, d MMM, yyyy').format(date);
  }

  Future<void> _showDeleteConfirmationDialog(
      BuildContext context, String advertId) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Advert'),
          content: const Text(
            'Are you sure you want to delete this advert? This action cannot be undone.',
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(
                textStyle: Theme.of(context).textTheme.labelLarge,
              ),
              child: Text(
                'Cancel',
                style: TextStyle(color: Theme.of(context).primaryColorLight),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              style: TextButton.styleFrom(
                textStyle: Theme.of(context).textTheme.labelLarge,
              ),
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
              onPressed: () async {
                Navigator.of(context).pop();
                Loader.show(context);

                final res = await ref
                    .read(adminDashboardServiceProvider)
                    .deleteAdvert(advertId);
                res.when(
                  (data) {
                    Toast.success('Advert deleted successfully');
                    Loader.hide();
                    ref
                        .read(adminDashboardControllerProvider.notifier)
                        .getAdverts();
                  },
                  (e) {
                    Toast.error(e.message);
                    Loader.hide();
                  },
                );
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final adminDashboardProvider = ref.watch(adminDashboardControllerProvider);
    final adminDashboardNotifier =
        ref.watch(adminDashboardControllerProvider.notifier);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Adverts'),
      ),
      body: adminDashboardProvider.adverts.when(
        data: (adverts) => _buildAdvertList(adverts),
        error: (e, s) => FailureWidget(
          fullScreen: true,
          e: e,
          retry: () => adminDashboardNotifier.getAdverts(),
        ),
        loading: () => Skeletonizer(
          enabled: true,
          child: _buildAdvertList(List.filled(1, Advert.defaultValue())),
        ),
      ),
    );
  }

  Widget _buildAdvertList(List<Advert> adverts) {
    if (adverts.isEmpty) {
      final primaryColorLight = Theme.of(context).primaryColorLight;
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.campaign_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No Adverts',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: primaryColorLight,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'There are no adverts in the system yet',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: primaryColorLight,
                  ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: adverts.length,
      itemBuilder: (context, index) {
        final advert = adverts[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Car Image
              ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(8)),
                child: advert.car.imageUrl != null
                    ? Image.network(
                        advert.car.imageUrl!,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        height: 200,
                        width: double.infinity,
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.directions_car,
                          size: 50,
                          color: Colors.white,
                        ),
                      ),
              ),
              Padding(
                padding: const EdgeInsets.all(16).copyWith(bottom: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Car Details
                    Text(
                      advert.car.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_formatDate(advert.startDate)} - ${_formatDate(advert.endDate)}',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[500],
                                  ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Status Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Active Status
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.check_circle,
                                size: 16,
                                color: Colors.green,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Active',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color: Colors.green,
                                    ),
                              ),
                            ],
                          ),
                        ),
                        // Approval Status
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: advert.isApproved
                                ? Colors.blue.withValues(alpha: 0.1)
                                : Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                advert.isApproved
                                    ? Icons.verified
                                    : Icons.pending,
                                size: 16,
                                color: advert.isApproved
                                    ? Colors.blue
                                    : Colors.orange,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                advert.isApproved ? 'Approved' : 'Pending',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color: advert.isApproved
                                          ? Colors.blue
                                          : Colors.orange,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
              // Delete Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () =>
                      _showDeleteConfirmationDialog(context, advert.id),
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  label: const Text('Delete Advert',
                      style: TextStyle(color: Colors.red)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.withValues(alpha: 0.1),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
