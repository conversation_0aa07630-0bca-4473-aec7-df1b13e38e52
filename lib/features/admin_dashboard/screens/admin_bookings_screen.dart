import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_booking_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AdminBookingsScreen extends ConsumerStatefulWidget {
  const AdminBookingsScreen({super.key});

  @override
  ConsumerState<AdminBookingsScreen> createState() => _AdminBookingsScreenState();
}

class _AdminBookingsScreenState extends ConsumerState<AdminBookingsScreen> {
  AsyncValue<List<Booking>> _rejectedBookings = const AsyncData([]);

  @override
  void initState() {
    super.initState();
    _fetchRejectedBookings();
  }

  Future<void> _fetchRejectedBookings() async {
    setState(() {
      _rejectedBookings = const AsyncLoading();
    });

    final res = await ref.read(adminBookingServiceProvider).getRejectedBookings();
    res.when(
      (bookings) {
        setState(() {
          _rejectedBookings = AsyncData(bookings);
        });
      },
      (e) {
        setState(() {
          _rejectedBookings = AsyncError(e, StackTrace.current);
        });
      },
    );
  }

  Widget _buildBookingCard(Booking booking) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    booking.car.name,
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withAlpha(51), // 0.2 opacity
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Declined',
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.error,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const YMargin(12),
            _buildInfoRow('Date Requested', booking.createdAt.toShortDate()),
            _buildInfoRow('Pickup Date', booking.startDate.toShortDate()),
            _buildInfoRow('Pickup Time', booking.startTime),
            _buildInfoRow('Drop-off Date', booking.endDate.toShortDate()),
            _buildInfoRow('Pickup Address', booking.pickupAddress.address ?? 'N/A'),
            _buildInfoRow('Destination', booking.destinationAddress.address ?? 'N/A'),
            _buildInfoRow('Total Price', CurrencyItem.formatAmountWithSymbol(context, booking.totalPrice)),
            _buildInfoRow('Booking Type', booking.bookingType),
            if (booking.escortCount > 0)
              _buildInfoRow('Police Escorts', booking.escortCount.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.secondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingList(List<Booking> bookings) {
    if (bookings.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_busy, size: 64, color: Colors.grey),
            YMargin(16),
            Text('No declined bookings found'),
            YMargin(8),
            Text(
              'Declined bookings will appear here',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        return _buildBookingCard(bookings[index]);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Declined Bookings'),
      ),
      body: RefreshIndicator(
        onRefresh: _fetchRejectedBookings,
        child: _rejectedBookings.when(
          data: (bookings) => _buildBookingList(bookings),
          error: (e, s) => FailureWidget(
            fullScreen: true,
            e: e,
            retry: _fetchRejectedBookings,
          ),
          loading: () => Skeletonizer(
            enabled: true,
            child: _buildBookingList(List.filled(3, Booking.defaultValue())),
          ),
        ),
      ),
    );
  }
}
