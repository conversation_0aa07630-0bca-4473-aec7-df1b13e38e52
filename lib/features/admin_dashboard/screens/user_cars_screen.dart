import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_dashboard_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:skeletonizer/skeletonizer.dart';

class UserCarsScreen extends ConsumerStatefulWidget {
  final String userId;
  final User user;

  const UserCarsScreen({
    super.key,
    required this.userId,
    required this.user,
  });

  @override
  ConsumerState<UserCarsScreen> createState() => _UserCarsScreenState();
}

class _UserCarsScreenState extends ConsumerState<UserCarsScreen> {
  AsyncValue<List<Car>> _cars = const AsyncData([]);

  @override
  void initState() {
    super.initState();
    _fetchUserCars();
  }

  Future<void> _fetchUserCars() async {
    setState(() {
      _cars = const AsyncLoading();
    });

    final res = await ref
        .read(adminDashboardServiceProvider)
        .getUserCars(widget.userId);

    res.when(
      (cars) {
        setState(() {
          _cars = AsyncData(cars);
        });
      },
      (e) {
        setState(() {
          _cars = AsyncError(e, StackTrace.current);
        });
      },
    );
  }

  String _formatDate(DateTime date) {
    return DateFormat('EEE, d MMM, yyyy').format(date);
  }

  Future<void> _showCreateAdvertDialog(BuildContext context, Car car) async {
    DateTime? startDate;
    DateTime? endDate;

    final result = await showDialog<Map<String, DateTime>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Create Advert',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 77)),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: ListTile(
                  leading: Icon(Icons.calendar_today,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 128)),
                  title: const Text(
                    'Start Date',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    startDate != null
                        ? _formatDate(startDate!)
                        : 'Select start date',
                    style: TextStyle(
                      color: startDate != null
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 128),
                    ),
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                      builder: (context, child) => Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: Theme.of(context).brightness ==
                                  Brightness.light
                              ? ColorScheme.light(
                                  primary:
                                      Theme.of(context).colorScheme.secondary,
                                  onPrimary:
                                      Theme.of(context).colorScheme.onSecondary,
                                  onSurface:
                                      Theme.of(context).colorScheme.onSurface,
                                  surface:
                                      Theme.of(context).colorScheme.surface,
                                )
                              : ColorScheme.dark(
                                  primary:
                                      Theme.of(context).colorScheme.secondary,
                                  onPrimary:
                                      Theme.of(context).colorScheme.onSecondary,
                                  onSurface:
                                      Theme.of(context).colorScheme.onSurface,
                                  surface:
                                      Theme.of(context).colorScheme.surface,
                                ),
                          dialogTheme: DialogThemeData(
                              backgroundColor:
                                  Theme.of(context).colorScheme.surface),
                        ),
                        child: child!,
                      ),
                    );
                    if (date != null) {
                      startDate = date;
                      (context as Element).markNeedsBuild();
                    }
                  },
                ),
              ),
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 77)),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: ListTile(
                  leading: Icon(Icons.calendar_today,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 128)),
                  title: const Text(
                    'End Date',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    endDate != null ? _formatDate(endDate!) : 'Select end date',
                    style: TextStyle(
                      color: endDate != null
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 128),
                    ),
                  ),
                  onTap: () async {
                    if (startDate == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please select start date first'),
                        ),
                      );
                      return;
                    }
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate!,
                      firstDate: startDate!,
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                      builder: (context, child) => Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: Theme.of(context).brightness ==
                                  Brightness.light
                              ? ColorScheme.light(
                                  primary:
                                      Theme.of(context).colorScheme.secondary,
                                  onPrimary:
                                      Theme.of(context).colorScheme.onSecondary,
                                  onSurface:
                                      Theme.of(context).colorScheme.onSurface,
                                  surface:
                                      Theme.of(context).colorScheme.surface,
                                )
                              : ColorScheme.dark(
                                  primary:
                                      Theme.of(context).colorScheme.secondary,
                                  onPrimary:
                                      Theme.of(context).colorScheme.onSecondary,
                                  onSurface:
                                      Theme.of(context).colorScheme.onSurface,
                                  surface:
                                      Theme.of(context).colorScheme.surface,
                                ),
                          dialogTheme: DialogThemeData(
                              backgroundColor:
                                  Theme.of(context).colorScheme.surface),
                        ),
                        child: child!,
                      ),
                    );
                    if (date != null) {
                      endDate = date;
                      (context as Element).markNeedsBuild();
                    }
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context)
                  .colorScheme
                  .onSurface
                  .withValues(alpha: 153), // ~0.6 opacity
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (startDate != null && endDate != null) {
                Navigator.pop(context, {
                  'startDate': startDate!,
                  'endDate': endDate!,
                });
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please select both start and end dates'),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Theme.of(context).colorScheme.onSecondary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            child: const Text('Create Advert'),
          ),
        ],
      ),
    );

    if (result != null) {
      if (!context.mounted) return;
      Loader.show(context);

      final res = await ref
          .read(adminDashboardServiceProvider)
          .createAdvert(car.id, result['startDate']!, result['endDate']!);
      res.when(
        (_) {
          Toast.success('Advert created successfully');
          Loader.hide();
          // if (mounted) {
          //   ScaffoldMessenger.of(context).showSnackBar(
          //     const SnackBar(content: Text('Advert created successfully')),
          //   );
          // }
        },
        (e) {
          Toast.error(e.message);
          Loader.hide();
          // if (mounted) {
          //   ScaffoldMessenger.of(showSnackBar(
          //     SnackBar(content: Text('Failed to create advert: $e')),
          //   );
          // }
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.user.fullName}\'s Cars'),
      ),
      body: _cars.when(
        data: (cars) => _buildUserCars(cars),
        error: (e, s) => FailureWidget(
          e: e,
          retry: _fetchUserCars,
        ),
        loading: () => Skeletonizer(
          enabled: true,
          child: _buildUserCars(List.filled(2, Car.defaultValue())),
        ),
      ),
    );
  }

  Widget _buildUserCars(List<Car> cars) {
    if (cars.isEmpty) {
      final primaryColorLight = Theme.of(context).primaryColorLight;
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.directions_car_outlined,
              size: 64,
              color: Theme.of(context)
                  .colorScheme
                  .onSurface
                  .withValues(alpha: 128),
            ),
            const SizedBox(height: 16),
            Text(
              'No Cars Found',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: primaryColorLight,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'This user has not added any cars yet',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: primaryColorLight,
                  ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: cars.length,
      itemBuilder: (context, index) {
        final car = cars[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: car.imageUrl != null
                          ? Image.network(
                              car.imageUrl!,
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              width: 100,
                              height: 100,
                              color: Theme.of(context)
                                  .colorScheme
                                  .surface
                                  .withValues(alpha: 128),
                              child: Icon(
                                Icons.directions_car,
                                size: 40,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${car.brand} ${car.model}',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Year: ${car.year}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Category: ${car.category}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Daily Min Price: ${CurrencyItem.formatAmountWithSymbol(context, car.dailyMinPrice)}',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Status: ${car.isAvailable ? "Available" : "Not Available"}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: car.isAvailable ? Colors.green : Colors.red,
                          ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => _showCreateAdvertDialog(context, car),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).colorScheme.secondary,
                        foregroundColor:
                            Theme.of(context).colorScheme.onSecondary,
                      ),
                      icon: const Icon(Icons.add_circle_outline),
                      label: const Text('Create Advert'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
