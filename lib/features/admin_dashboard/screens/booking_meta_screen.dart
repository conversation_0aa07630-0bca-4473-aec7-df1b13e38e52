import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_booking_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class BookingMetaScreen extends ConsumerStatefulWidget {
  const BookingMetaScreen({super.key});

  @override
  ConsumerState<BookingMetaScreen> createState() => _BookingMetaScreenState();
}

class _BookingMetaScreenState extends ConsumerState<BookingMetaScreen> {
  AsyncValue<BookingMeta> _bookingMeta = AsyncData(BookingMeta.defaultValue());

  @override
  void initState() {
    super.initState();
    _fetchBookingMeta();
  }

  Future<void> _fetchBookingMeta() async {
    setState(() {
      _bookingMeta = const AsyncLoading();
    });

    final res = await ref.read(adminBookingServiceProvider).getBookingMeta();
    res.when(
      (meta) {
        setState(() {
          _bookingMeta = AsyncData(meta);
        });
      },
      (e) {
        setState(() {
          _bookingMeta = AsyncError(e, StackTrace.current);
        });
      },
    );
  }

  Widget _buildMetaCard({
    required String title,
    required int count,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              icon,
              size: 40,
              color: color,
            ),
            const YMargin(12),
            Text(
              count.toString(),
              style: textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const YMargin(8),
            Text(
              title,
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetaGrid(BookingMeta meta) {
    final colorScheme = Theme.of(context).colorScheme;

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      padding: const EdgeInsets.all(16),
      children: [
        _buildMetaCard(
          title: 'Total Bookings',
          count: meta.totalBookings,
          icon: Icons.event,
          color: colorScheme.primary,
        ),
        _buildMetaCard(
          title: 'Pending',
          count: meta.totalBookingsPending,
          icon: Icons.pending,
          color: Colors.orange,
        ),
        _buildMetaCard(
          title: 'Pending Payment',
          count: meta.totalBookingsPendingPayment,
          icon: Icons.payment,
          color: Colors.amber,
        ),
        _buildMetaCard(
          title: 'Pending Approval',
          count: meta.totalBookingsPendingApproval,
          icon: Icons.approval,
          color: Colors.blue,
        ),
        _buildMetaCard(
          title: 'Paid',
          count: meta.totalBookingsPaid,
          icon: Icons.paid,
          color: Colors.green,
        ),
        _buildMetaCard(
          title: 'Approved',
          count: meta.totalBookingsApproved,
          icon: Icons.check_circle,
          color: Colors.teal,
        ),
        _buildMetaCard(
          title: 'Declined',
          count: meta.totalBookingsDeclined,
          icon: Icons.cancel,
          color: Colors.red,
        ),
        _buildMetaCard(
          title: 'Cancelled',
          count: meta.totalBookingsCancelled,
          icon: Icons.block,
          color: Colors.grey,
        ),
        _buildMetaCard(
          title: 'In Progress',
          count: meta.totalBookingsInProgress,
          icon: Icons.directions_car,
          color: Colors.purple,
        ),
        _buildMetaCard(
          title: 'Completed',
          count: meta.totalBookingsCompleted,
          icon: Icons.done_all,
          color: Colors.indigo,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Statistics'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchBookingMeta,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _fetchBookingMeta,
        child: _bookingMeta.when(
          data: (meta) => _buildMetaGrid(meta),
          error: (e, s) => FailureWidget(
            fullScreen: true,
            e: e,
            retry: _fetchBookingMeta,
          ),
          loading: () => Skeletonizer(
            enabled: true,
            child: _buildMetaGrid(BookingMeta.defaultValue()),
          ),
        ),
      ),
    );
  }
}
