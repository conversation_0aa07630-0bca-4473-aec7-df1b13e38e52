import 'package:fleet_mobile/core/routes/route_constants.dart';
import 'package:fleet_mobile/core/widgets/margin.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class AdminDashboard extends StatelessWidget {
  const AdminDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            const SliverAppBar(
              leading: BackButton(),
              title: Text('Admin Dashboard'),
              pinned: true,
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const YMargin(24),
                    _buildDashboardCard(
                      context,
                      title: 'All Users',
                      subtitle: 'View and manage all users',
                      icon: Icons.people,
                      onTap: () => context.pushNamed(adminUsersPath),
                    ),
                    Consumer(
                      builder: (context, ref, child) {
                        final user = ref.watch(authControllerProvider).user;

                        return user?.isSuperAdmin == true
                            ? Column(
                                children: [
                                  const YMargin(16),
                                  _buildDashboardCard(
                                    context,
                                    title: 'Admin Users',
                                    subtitle: 'View and manage admin users',
                                    icon: Icons.admin_panel_settings,
                                    onTap: () =>
                                        context.pushNamed(adminAdminsPath),
                                  ),
                                  const YMargin(16),
                                  _buildDashboardCard(
                                    context,
                                    title: 'Super Admin Users',
                                    subtitle: 'View super admin users',
                                    icon: Icons.admin_panel_settings,
                                    onTap: () =>
                                        context.pushNamed(adminSuperAdminsPath),
                                  ),
                                  const YMargin(16),
                                  _buildDashboardCard(
                                    context,
                                    title: 'Booking Statistics',
                                    subtitle: 'View booking analytics',
                                    icon: Icons.analytics,
                                    onTap: () =>
                                        context.pushNamed(adminBookingMetaPath),
                                  ),
                                ],
                              )
                            : const SizedBox.shrink();
                      },
                    ),
                    const YMargin(16),
                    _buildDashboardCard(
                      context,
                      title: 'Adverts',
                      subtitle: 'View and manage adverts',
                      icon: Icons.campaign,
                      onTap: () => context.pushNamed(advertListPath),
                    ),
                    const YMargin(16),
                    _buildDashboardCard(
                      context,
                      title: 'Brand Management',
                      subtitle: 'Manage car brands',
                      icon: Icons.branding_watermark,
                      onTap: () => context.pushNamed(adminBrandsPath),
                    ),
                    const YMargin(16),
                    _buildDashboardCard(
                      context,
                      title: 'Category Management',
                      subtitle: 'Manage car categories',
                      icon: Icons.category,
                      onTap: () => context.pushNamed(adminCategoriesPath),
                    ),
                    const YMargin(16),
                    _buildDashboardCard(
                      context,
                      title: 'Declined Bookings',
                      subtitle: 'View all declined bookings',
                      icon: Icons.event_busy,
                      onTap: () => context.pushNamed(adminBookingsPath),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    Color iconColor;

    // Set specific colors for different types of users
    if (title == 'Admin Users') {
      iconColor = Colors.blue;
    } else if (title == 'Super Admin Users') {
      iconColor = Colors.purple;
    } else if (title == 'Adverts') {
      iconColor = Colors.orange;
    } else if (title == 'Brand Management') {
      iconColor = Colors.teal;
    } else if (title == 'Category Management') {
      iconColor = Colors.indigo;
    } else if (title == 'Declined Bookings') {
      iconColor = Colors.red;
    } else if (title == 'Booking Statistics') {
      iconColor = Colors.green;
    } else {
      iconColor = colorScheme.secondary;
    }

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(icon, size: 32, color: iconColor),
              const XMargin(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const YMargin(4),
                    Text(
                      subtitle,
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium
                          ?.copyWith(color: colorScheme.onSurface
                              // .withValues(alpha: 179), // ~0.7 opacity
                              ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.chevron_right,
                  color: colorScheme.onSurface
                      .withValues(alpha: 179)), // ~0.7 opacity
            ],
          ),
        ),
      ),
    );
  }
}
