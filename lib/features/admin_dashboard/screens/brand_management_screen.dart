import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_brand_category_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:wc_form_validators/wc_form_validators.dart';

class BrandManagementScreen extends ConsumerStatefulWidget {
  const BrandManagementScreen({super.key});

  @override
  ConsumerState<BrandManagementScreen> createState() =>
      _BrandManagementScreenState();
}

class _BrandManagementScreenState extends ConsumerState<BrandManagementScreen> {
  AsyncValue<List<Brand>> _brands = const AsyncData([]);

  @override
  void initState() {
    super.initState();
    _fetchBrands();
  }

  Future<void> _fetchBrands() async {
    setState(() {
      _brands = const AsyncLoading();
    });

    final res = await ref.read(adminBrandCategoryServiceProvider).getBrands();
    res.when(
      (brands) {
        setState(() {
          _brands = AsyncData(brands);
        });
      },
      (e) {
        setState(() {
          _brands = AsyncError(e, StackTrace.current);
        });
      },
    );
  }

  Future<void> _showCreateBrandDialog() async {
    final nameController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Brand'),
        content: Form(
          key: formKey,
          child: LuxTextField(
            textFieldType: TextFieldType.text,
            title: 'Brand Name',
            hint: 'Enter brand name',
            textFieldController: nameController,
            isRequired: true,
            validator: [Validators.required('Brand name is required')],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context, true);
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );

    if (result == true && nameController.text.isNotEmpty) {
      Loader.show(context);
      final res = await ref
          .read(adminBrandCategoryServiceProvider)
          .createBrand(nameController.text.trim());

      res.when(
        (brand) {
          Loader.hide();
          Toast.success('Brand created successfully');
          _fetchBrands();
        },
        (error) {
          Loader.hide();
          Toast.error(error.message);
        },
      );
    }
  }

  Future<void> _showEditBrandDialog(Brand brand) async {
    final nameController = TextEditingController(text: brand.name);
    final formKey = GlobalKey<FormState>();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Brand'),
        content: Form(
          key: formKey,
          child: LuxTextField(
            textFieldType: TextFieldType.text,
            title: 'Brand Name',
            hint: 'Enter brand name',
            textFieldController: nameController,
            isRequired: true,
            validator: [Validators.required('Brand name is required')],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context, true);
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );

    if (result == true && nameController.text.isNotEmpty) {
      Loader.show(context);
      final res = await ref
          .read(adminBrandCategoryServiceProvider)
          .updateBrand(brand.id, nameController.text.trim());

      res.when(
        (updatedBrand) {
          Loader.hide();
          Toast.success('Brand updated successfully');
          _fetchBrands();
        },
        (error) {
          Loader.hide();
          Toast.error(error.message);
        },
      );
    }
  }

  Future<void> _deleteBrand(Brand brand) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Brand'),
        content: Text('Are you sure you want to delete "${brand.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (result == true) {
      Loader.show(context);
      final res = await ref
          .read(adminBrandCategoryServiceProvider)
          .deleteBrand(brand.id);

      res.when(
        (_) {
          Loader.hide();
          Toast.success('Brand deleted successfully');
          _fetchBrands();
        },
        (error) {
          Loader.hide();
          Toast.error(error.message);
        },
      );
    }
  }

  Widget _buildBrandList(List<Brand> brands) {
    if (brands.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.branding_watermark, size: 64, color: Colors.grey),
            YMargin(16),
            Text('No brands found'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: brands.length,
      itemBuilder: (context, index) {
        final brand = brands[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: const CircleAvatar(
              child: Icon(Icons.branding_watermark),
            ),
            title: Text(brand.name),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditBrandDialog(brand),
                  tooltip: 'Edit Brand',
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _deleteBrand(brand),
                  tooltip: 'Delete Brand',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Brand Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showCreateBrandDialog,
            tooltip: 'Add Brand',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _fetchBrands,
        child: _brands.when(
          data: (brands) => _buildBrandList(brands),
          error: (e, s) => FailureWidget(
            fullScreen: true,
            e: e,
            retry: _fetchBrands,
          ),
          loading: () => Skeletonizer(
            enabled: true,
            child: _buildBrandList(List.filled(5, Brand.defaultBrand())),
          ),
        ),
      ),
    );
  }
}
