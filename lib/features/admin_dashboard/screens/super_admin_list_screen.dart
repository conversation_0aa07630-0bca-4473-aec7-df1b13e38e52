import 'package:fleet_mobile/core/models/user.dart';
import 'package:fleet_mobile/core/screens/failure_screen.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_dashboard_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SuperAdminListScreen extends ConsumerStatefulWidget {
  const SuperAdminListScreen({super.key});

  @override
  ConsumerState<SuperAdminListScreen> createState() =>
      _SuperAdminListScreenState();
}

class _SuperAdminListScreenState extends ConsumerState<SuperAdminListScreen> {
  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      ref.read(adminDashboardControllerProvider.notifier).getSuperAdmins();
    });
  }

  @override
  Widget build(BuildContext context) {
    final adminDashboardProvider = ref.watch(adminDashboardControllerProvider);
    final adminDashboardNotifier =
        ref.watch(adminDashboardControllerProvider.notifier);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Super Admin Users'),
      ),
      body: adminDashboardProvider.superAdmins.when(
        data: (superAdmins) => _buildSuperAdminList(superAdmins),
        error: (e, s) => FailureWidget(
          fullScreen: true,
          e: e,
          retry: () => adminDashboardNotifier.getSuperAdmins(),
        ),
        loading: () => Skeletonizer(
          enabled: true,
          child: _buildSuperAdminList(List.filled(5, User.defaultValue())),
        ),
      ),
    );
  }

  Widget _buildSuperAdminList(List<User> superAdmins) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: superAdmins.length,
      itemBuilder: (context, index) {
        final superAdmin = superAdmins[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: const CircleAvatar(
              backgroundColor: Colors.purple,
              child: Icon(Icons.admin_panel_settings, color: Colors.white),
            ),
            title: Text(superAdmin.fullName),
            subtitle: Text(superAdmin.email),
          ),
        );
      },
    );
  }
}
