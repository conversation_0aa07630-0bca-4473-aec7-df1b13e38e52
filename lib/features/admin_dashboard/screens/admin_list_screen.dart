import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_dashboard_controller.dart';
import 'package:fleet_mobile/features/admin_dashboard/admin_dashboard_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AdminListScreen extends ConsumerStatefulWidget {
  const AdminListScreen({super.key});

  @override
  ConsumerState<AdminListScreen> createState() => _AdminListScreenState();
}

class _AdminListScreenState extends ConsumerState<AdminListScreen> {
  @override
  void initState() {
    super.initState();
    _fetchAdmins();
  }

  void _fetchAdmins() {
    Future.microtask(() {
      ref.read(adminDashboardControllerProvider.notifier).getAdmins();
    });
  }

  @override
  Widget build(BuildContext context) {
    final adminDashboardProvider = ref.watch(adminDashboardControllerProvider);
    final adminDashboardNotifier =
        ref.watch(adminDashboardControllerProvider.notifier);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Users'),
      ),
      body: adminDashboardProvider.admins.when(
        data: (admins) => _buildAdminList(admins),
        error: (e, s) => FailureWidget(
            fullScreen: true,
            e: e,
            retry: () => adminDashboardNotifier.getAdmins()),
        loading: () => Skeletonizer(
          enabled: true,
          child: _buildAdminList(List.filled(5, User.defaultValue())),
        ),
      ),
    );
  }

  Widget _buildAdminList(List<User> admins) {
    if (admins.isEmpty) {
      final primaryColorLight = Theme.of(context).primaryColorLight;
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.admin_panel_settings_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No Admin Users',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: primaryColorLight,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'There are no admin users in the system yet',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: primaryColorLight,
                  ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: admins.length,
      itemBuilder: (context, index) {
        final admin = admins[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: const CircleAvatar(
              backgroundColor: Colors.blue,
              child: Icon(Icons.admin_panel_settings, color: Colors.white),
            ),
            title: Text(admin.fullName),
            subtitle: Text(admin.email),
            trailing: IconButton(
              icon: const Icon(Icons.remove_circle_outline, color: Colors.red),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Revoke Admin Access'),
                    content: const Text(
                      'Are you sure you want to revoke admin access for this user?',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                              color: Theme.of(context).primaryColorLight),
                        ),
                      ),
                      TextButton(
                        onPressed: () async {
                          Navigator.pop(context);

                          Loader.show(context);

                          final res = await ref
                              .read(adminDashboardServiceProvider)
                              .updateUserRole(admin.id,
                                  admin.isAdmin ? 'manager' : 'admin');

                          res.when((data) {
                            _fetchAdmins();
                            Toast.success('User role updated successfully');
                            Loader.hide();
                          }, (e) {
                            Toast.error(e.message);
                            Loader.hide();
                          });
                        },
                        child: const Text('Revoke',
                            style: TextStyle(color: Colors.red)),
                      ),
                    ],
                  ),
                );
              },
              tooltip: 'Revoke Admin',
            ),
          ),
        );
      },
    );
  }
}
