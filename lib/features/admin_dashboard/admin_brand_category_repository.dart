import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final adminBrandCategoryRepositoryProvider =
    Provider<AdminBrandCategoryRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return LuxAdminBrandCategoryRepository(dio);
});

abstract class AdminBrandCategoryRepository {
  // Brand operations
  Future<List<Brand>> getBrands();
  Future<Brand> createBrand(String name);
  Future<Brand> updateBrand(String id, String name);
  Future<void> deleteBrand(String id);

  // Category operations
  Future<List<Category>> getCategories();
  Future<Category> createCategory(String name, String imagePath);
  Future<Category> updateCategory(String id, String name);
  Future<void> deleteCategory(String id);
}

class LuxAdminBrandCategoryRepository implements AdminBrandCategoryRepository {
  LuxAdminBrandCategoryRepository(this.dio);

  final Dio dio;

  @override
  Future<List<Brand>> getBrands() async {
    return await dioInterceptor(() async {
      final res = await dio.get('brands');
      final List<dynamic> brands = res.data['data'];
      return brands.map((x) => Brand.fromMap(x)).toList();
    });
  }

  @override
  Future<Brand> createBrand(String name) async {
    return await dioInterceptor(() async {
      final res = await dio.post('brands', data: {'name': name});
      return Brand.fromMap(res.data['data']);
    });
  }

  @override
  Future<Brand> updateBrand(String id, String name) async {
    return await dioInterceptor(() async {
      final res = await dio.put('brand/$id', data: {'name': name});
      return Brand.fromMap(res.data['data']);
    });
  }

  @override
  Future<void> deleteBrand(String id) async {
    return await dioInterceptor(() async {
      await dio.delete('brand/$id');
    });
  }

  @override
  Future<List<Category>> getCategories() async {
    return await dioInterceptor(() async {
      final res = await dio.get('car/categories/get');
      final List<dynamic> categories = res.data['data'];
      return categories.map((x) => Category.fromMap(x)).toList();
    });
  }

  @override
  Future<Category> createCategory(String name, String imagePath) async {
    return await dioInterceptor(() async {
      final formData = FormData.fromMap({
        'name': name,
        'image': await MultipartFile.fromFile(imagePath),
      });

      final res = await dio.post('cars/category', data: formData);
      return Category.fromMap(res.data['data']);
    });
  }

  @override
  Future<Category> updateCategory(String id, String name) async {
    return await dioInterceptor(() async {
      final res = await dio.put('cars/category/$id', data: {'name': name});
      return Category.fromMap(res.data['data']);
    });
  }

  @override
  Future<void> deleteCategory(String id) async {
    return await dioInterceptor(() async {
      await dio.delete('cars/category/$id');
    });
  }
}
