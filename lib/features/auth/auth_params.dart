import 'dart:convert';
import 'package:fleet_mobile/core/core.dart';

class LoginParams {
  /// this represents the email or phone number
  final String username;
  final String password;
  final String deviceId;
  final String fcmToken;
  LoginParams({
    required this.username,
    required this.password,
    required this.deviceId,
    required this.fcmToken,
  });

  Map<String, dynamic> toMap() {
    final data = {
      'password': password,
      // 'deviceId': deviceId,
      // 'fcmToken': fcmToken,
    };

    if (isNumeric(username)) {
      data['phoneNumber'] = username;
    } else {
      data['email'] = username;
    }

    return data;
  }
}

class SignUpParams {
  final String firstName;
  final String? middleName;
  final String lastName;
  final String email;
  final String phoneNumber;
  final String password;
  final String deviceId;
  final String fcmToken;
  final LuxAddress address;
  final String role;
  SignUpParams({
    required this.firstName,
    this.middleName,
    required this.lastName,
    required this.email,
    required this.phoneNumber,
    required this.password,
    required this.deviceId,
    required this.fcmToken,
    required this.address,
    this.role = 'manager',
  });

  Map<String, dynamic> toMap() {
    return {
      'firstName': firstName,
      if (middleName != null) 'middleName': middleName,
      'lastName': lastName,
      'email': email,
      'phoneNumber': phoneNumber,
      'password': password,
      'deviceId': deviceId,
      'fcmToken': fcmToken,
      'role': role,
      'address': address.toMap(),
    };
  }
}

class UpdateUserParams {
  final String firstName;
  final String? middleName;
  final String lastName;
  UpdateUserParams({
    required this.firstName,
    this.middleName,
    required this.lastName,
  });

  Map<String, dynamic> toMap() {
    return {
      'firstName': firstName,
      'middleName': middleName,
      'lastName': lastName,
    };
  }

  String toJson() => json.encode(toMap());

  factory UpdateUserParams.fromMap(Map<String, dynamic> map) {
    return UpdateUserParams(
      firstName: map['firstName'],
      middleName: map['middleName'],
      lastName: map['lastName'],
    );
  }

  UpdateUserParams copyWith({
    String? firstName,
    String? middleName,
    String? lastName,
  }) {
    return UpdateUserParams(
      firstName: firstName ?? this.firstName,
      middleName: middleName ?? this.middleName,
      lastName: lastName ?? this.lastName,
    );
  }
}

class EmailOrPhoneParams {
  final String username;

  EmailOrPhoneParams({
    required this.username,
  });

  EmailOrPhoneParams copyWith({
    String? username,
  }) {
    return EmailOrPhoneParams(
      username: username ?? this.username,
    );
  }

  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = {};

    if (isNumeric(username)) {
      data['phoneNumber'] = username;
    } else {
      data['email'] = username;
    }

    return data;
  }
}

class VerifyOtpParams {
  final String username;
  final String otp;

  VerifyOtpParams({
    required this.username,
    required this.otp,
  });

  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = {
      'otp': otp,
    };

    if (isNumeric(username)) {
      data['phoneNumber'] = username;
    } else {
      data['email'] = username;
    }

    return data;
  }
}

class ResetPasswordParams {
  final String newPassword;
  final String authToken;
  ResetPasswordParams({
    required this.newPassword,
    required this.authToken,
  });

  Map<String, dynamic> toMap() {
    return {
      'newPassword': newPassword,
    };
  }
}

class ChangePasswordParams {
  final String oldPassword;
  final String newPassword;
  ChangePasswordParams({
    required this.oldPassword,
    required this.newPassword,
  });

  Map<String, dynamic> toMap() {
    return {
      'oldPassword': oldPassword,
      'newPassword': newPassword,
    };
  }
}

class VerifyOtpResponse {
  final String authToken;
  VerifyOtpResponse({
    required this.authToken,
  });
}
