import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class FormScreen extends StatelessWidget {
  const FormScreen({
    super.key,
    required this.formKey,
    required this.handleSubmit,
    required this.body,
    required this.title,
    required this.submitButtonText,
    this.footer,
    this.showLogoImage = true,
    this.disableSubmitButton = false,
    this.autoValidateMode,
  });

  final GlobalKey<FormState> formKey;
  final VoidCallback handleSubmit;
  final List<Widget> body;
  final String title;
  final String submitButtonText;
  final Widget? footer;
  final bool showLogoImage;
  final bool? disableSubmitButton;
  final AutovalidateMode? autoValidateMode;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return SafeArea(
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            if (context.canPop())
              const SliverAppBar(
                leading: BackButton(),
              ),
            SliverFillRemaining(
              child: Form(
                key: formKey,
                autovalidateMode: autoValidateMode,
                child: Center(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (showLogoImage) ...[
                            Image.asset(
                              kImgBrandAdaptiveIcon,
                              // fit: BoxFit.scaleDown,
                              width: 80,
                              height: 80,
                            ),
                            const YMargin(30)
                          ],
                          Text(
                            title,
                            style: textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const YMargin(30),
                          ...body,
                          const YMargin(20),
                          LuxButton(
                            onTap: handleSubmit,
                            text: submitButtonText,
                            disabled: disableSubmitButton,
                          ),
                          const YMargin(16),
                          if (footer != null) footer!,
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
