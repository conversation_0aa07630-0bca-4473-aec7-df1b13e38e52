import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import 'package:fleet_mobile/core/models/user.dart';

@immutable
class AuthState extends Equatable {
  final User? user;
  final String? authToken;
  // final int? expiresIn;

  const AuthState({
    this.user,
    this.authToken,
    // this.expiresIn,
  });

  bool get isLoggedIn => authToken != null && authToken!.isNotEmpty;

  AuthState copyWith({
    User? user,
  }) {
    return AuthState(
      user: user ?? this.user,
      authToken: user?.authToken ?? this.user?.authToken,
      // expiresIn: user?.authTokenExp ?? this.user?.authTokenExp,
    );
  }

  @override
  List<Object?> get props => [user, authToken];
}
