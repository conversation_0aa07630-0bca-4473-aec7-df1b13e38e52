import 'package:fleet_mobile/core/core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'auth_params.dart';
import 'auth_repository.dart';

final authServiceProvider = Provider<AuthService>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return LuxAuthService(
    authRepository,
  );
});

abstract class AuthService {
  Future<Result<User, Failure>> signUp(SignUpParams params);
  Future<Result<User, Failure>> login(LoginParams params);
  Future<Result<Unit, Failure>> sendOtp(EmailOrPhoneParams params);
  Future<Result<VerifyOtpResponse, Failure>> verifyOtp(VerifyOtpParams params);
  Future<Result<String, Failure>> changePassword(ChangePasswordParams params);
  Future<Result<Unit, Failure>> resetPassword(ResetPasswordParams params);
  Future<Result<Unit, Failure>> addPaymentInfo(Map<String, dynamic> params);
  Future<Result<Unit, Failure>> updateUserProfile(
      String userId, Map<String, dynamic> params);
  Future<Result<Wallet, Failure>> fetchWallet();
  Future<Result<User, Failure>> getUser();
}

class LuxAuthService implements AuthService {
  final AuthRepository _authRepository;

  LuxAuthService(this._authRepository);

  @override
  Future<Result<User, Failure>> signUp(SignUpParams params) async {
    try {
      final res = await _authRepository.signUp(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<User, Failure>> login(LoginParams params) async {
    try {
      final res = await _authRepository.login(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> sendOtp(EmailOrPhoneParams params) async {
    try {
      final res = await _authRepository.sendOtp(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<VerifyOtpResponse, Failure>> verifyOtp(
      VerifyOtpParams params) async {
    try {
      final res = await _authRepository.verifyOtp(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<String, Failure>> changePassword(
      ChangePasswordParams params) async {
    try {
      final res = await _authRepository.changePassword(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> resetPassword(
      ResetPasswordParams params) async {
    try {
      final res = await _authRepository.resetPassword(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> addPaymentInfo(
      Map<String, dynamic> params) async {
    try {
      final res = await _authRepository.addPaymentInfo(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> updateUserProfile(
      String userId, Map<String, dynamic> params) async {
    try {
      final res = await _authRepository.updateUserProfile(userId, params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Wallet, Failure>> fetchWallet() async {
    try {
      final res = await _authRepository.fetchWallet();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<User, Failure>> getUser() async {
    try {
      final res = await _authRepository.getUser();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }
}
