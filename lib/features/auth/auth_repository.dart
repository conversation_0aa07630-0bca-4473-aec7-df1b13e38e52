import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'auth_params.dart';

final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return LuxAuthRepository(dio);
});

abstract class AuthRepository {
  Future<User> signUp(SignUpParams params);
  Future<User> login(LoginParams params);
  Future<Unit> sendOtp(EmailOrPhoneParams params);
  Future<VerifyOtpResponse> verifyOtp(VerifyOtpParams params);
  Future<String> changePassword(ChangePasswordParams params);
  Future<Unit> resetPassword(ResetPasswordParams params);
  Future<Unit> addPaymentInfo(Map<String, dynamic> params);
  Future<Unit> updateUserProfile(String userId, Map<String, dynamic> params);
  Future<Wallet> fetchWallet();
  Future<User> getUser();
}

class LuxAuthRepository implements AuthRepository {
  LuxAuthRepository(this.dio);

  final Dio dio;

  @override
  Future<User> signUp(SignUpParams params) async {
    return await dioInterceptor(() async {
      final res = await dio.post('auth/fleet/create', data: params.toMap());

      final data = Map<String, dynamic>.from(res.data['data']);
      final user = User.fromMap(data);
      return user;
    });
  }

  @override
  Future<User> login(LoginParams params) async {
    return await dioInterceptor(() async {
      final res = await dio.post('auth/login', data: params.toMap());
      final userMap = res.data['data'];
      final user = User.fromMap(userMap)
          .copyWith(metaData: UserMetaData.fromMap(res.data['meta']));
      return user;
    });
  }

  @override
  Future<Unit> sendOtp(EmailOrPhoneParams params) async {
    return await dioInterceptor(() async {
      await dio.post('auth/send-otp', data: params.toMap());
      return unit;
    });
  }

  @override
  Future<VerifyOtpResponse> verifyOtp(VerifyOtpParams params) async {
    return await dioInterceptor(() async {
      final res = await dio.post('auth/verify-otp', data: params.toMap());
      return VerifyOtpResponse(authToken: res.data['authToken']);
    });
  }

  @override
  Future<String> changePassword(ChangePasswordParams params) async {
    return await dioInterceptor(() async {
      final res = await dio.post('auth/change-password', data: params.toMap());
      return res.data['authToken'];
    });
  }

  @override
  Future<Unit> resetPassword(ResetPasswordParams params) async {
    final dio =
        createDioInstance(baseUrl: config.baseUrl, authToken: params.authToken);
    return await dioInterceptor(() async {
      await dio.post('auth/reset-password', data: params.toMap());
      return unit;
    });
  }

  @override
  Future<Unit> addPaymentInfo(Map<String, dynamic> params) async {
    return await dioInterceptor(() async {
      await dio.post('payments', data: params);
      return unit;
    });
  }

  @override
  Future<Unit> updateUserProfile(
      String userId, Map<String, dynamic> params) async {
    return await dioInterceptor(() async {
      await dio.put('users/me', data: params);
      return unit;
    });
  }

  @override
  Future<Wallet> fetchWallet() async {
    return await dioInterceptor(() async {
      final res = await dio.get('wallets');
      return Wallet.fromMap(res.data['data']);
    });
  }

  @override
  Future<User> getUser() async {
    return await dioInterceptor(() async {
      final res = await dio.get('users/me');
      final userMap = res.data['data'];
      final user = User.fromMap(userMap)
          .copyWith(metaData: UserMetaData.fromMap(res.data['meta']));
      return user;
    });
  }
}
