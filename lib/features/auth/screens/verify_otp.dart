import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_params.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';

class VerifyOtpScreen extends ConsumerStatefulWidget {
  const VerifyOtpScreen(this.args, {super.key});

  final EmailOrPhoneParams args;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _VerifyOtpScreenState();
}

class _VerifyOtpScreenState extends ConsumerState<VerifyOtpScreen> {
  final controller = TextEditingController();
  final focusNode = FocusNode();
  bool canSubmit = false;
  bool showError = false;
  String? errorText;

  @override
  void dispose() {
    controller.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    const length = 6;
    final defaultPinTheme = PinTheme(
      width: 48,
      height: 48,
      textStyle: textTheme.bodyLarge?.copyWith(
        fontSize: 20,
        color: Palette.kFFFFFF,
        fontWeight: FontWeight.w600,
      ),
      padding: const EdgeInsets.only(top: 5),
      decoration: BoxDecoration(
        color: Palette.kFFFFFF.withValues(alpha: 0.22),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.transparent),
      ),
    );

    return SafeArea(
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            SliverAppBar(
              leading: BackButton(
                onPressed: () {
                  if (GoRouter.of(context).canPop()) {
                    context.pop();
                  } else {
                    context.goNamed(loginPath);
                  }
                },
              ),
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const YMargin(10),
                    Text(
                      'Reset password',
                      style: textTheme.bodyLarge?.copyWith(
                        color: Palette.kFFEABC,
                        fontSize: 20,
                      ),
                    ),
                    const YMargin(4),
                    Text(
                      'Enter the otp send to your email or phone,',
                      style: textTheme.bodyMedium,
                    ),
                    const YMargin(40),
                    Center(
                        child: SizedBox(
                      height: 68,
                      child: Pinput(
                        autofocus: true,
                        length: length,
                        controller: controller,
                        focusNode: focusNode,
                        defaultPinTheme: defaultPinTheme,
                        keyboardType: TextInputType.number,
                        // androidSmsAutofillMethod:
                        //     AndroidSmsAutofillMethod.smsRetrieverApi,
                        // androidSmsAutofillMethod:
                        //     AndroidSmsAutofillMethod.smsUserConsentApi,
                        forceErrorState: showError,
                        errorText: errorText,
                        onCompleted: (otp) {
                          setState(() => canSubmit = true);
                        },
                        onChanged: (otp) {
                          if (otp.length != length && canSubmit) {
                            setState(() => canSubmit = false);
                          }

                          if (showError || errorText != null) {
                            setState(() {
                              showError = false;
                              errorText = null;
                            });
                          }
                        },
                        focusedPinTheme: defaultPinTheme.copyWith(
                          height: 48,
                          width: 48,
                          decoration: defaultPinTheme.decoration!.copyWith(
                            border: Border.all(color: Palette.kFFEABC),
                          ),
                        ),
                        errorPinTheme: defaultPinTheme.copyWith(
                          decoration: BoxDecoration(
                            color: Palette.kCF3333,
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    )),
                    const Spacer(),
                    Center(
                      child: LuxButton(
                        disabled: !canSubmit,
                        onTap: () async {
                          Loader.show(context);

                          final otp = controller.text;

                          final res =
                              await ref.read(authServiceProvider).verifyOtp(
                                    VerifyOtpParams(
                                      username: widget.args.username,
                                      otp: otp,
                                    ),
                                  );

                          res.when(
                            (data) {
                              context.pushNamed(resetPasswordPath,
                                  extra: data.authToken);
                              Loader.hide();
                            },
                            (error) {
                              Loader.hide();
                              Toast.error(error.message);
                            },
                          );
                        },
                        text: 'Verify',
                      ),
                    ),
                    const YMargin(30),
                    Center(
                      child: Text.rich(
                        TextSpan(
                          text: '',
                          children: [
                            WidgetSpan(
                              child: Text(
                                "Didn't get code?",
                                style: textTheme.bodyMedium,
                              ),
                            ),
                            WidgetSpan(
                              child: InkWell(
                                onTap: () async {
                                  Loader.show(context);

                                  final res = await ref
                                      .read(authServiceProvider)
                                      .sendOtp(widget.args);

                                  res.when(
                                    (data) {
                                      controller.clear();
                                      Loader.hide();
                                      Toast.success(
                                          'OTP was sent successfully');
                                    },
                                    (error) {
                                      Loader.hide();
                                      Toast.error(error.message);
                                    },
                                  );
                                },
                                child: Text(
                                  'Resend',
                                  style: textTheme.bodyMedium?.copyWith(
                                    color: Palette.kFFB800,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        style: textTheme.bodyMedium,
                      ),
                    ),
                    const YMargin(20),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
