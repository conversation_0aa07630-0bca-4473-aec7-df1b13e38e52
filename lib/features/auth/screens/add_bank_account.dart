import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:fleet_mobile/features/auth/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:wc_form_validators/wc_form_validators.dart';

class AddBankAccount extends ConsumerWidget {
  AddBankAccount({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authControllerProvider).user!;
    final hasSetupPayment = user.hasSetupPayment;
    final paymentInfo = user.paymentInfo;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (paymentInfo?.accountName != null) {
        controllers['accountName']?.text = paymentInfo!.accountName;
      }
      if (paymentInfo?.accountNumber != null) {
        controllers['accountNumber']?.text = paymentInfo!.accountNumber;
      }
      if (paymentInfo?.bankName != null) {
        controllers['bankName']?.text = paymentInfo!.bankName;
      }
    });

    return FormScreen(
      formKey: formKey,
      title: 'Add Bank Account',
      submitButtonText: 'Add Account',
      handleSubmit: () => handleSubmit(context, ref),
      disableSubmitButton: hasSetupPayment,
      body: [
        LuxTextField(
          title: 'Account Name',
          hint: 'Enter account name',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.text,
          textFieldController: controllers['accountName'],
          isRequired: true,
          readonly: hasSetupPayment,
          filled: hasSetupPayment,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
        LuxTextField(
          title: 'Account Number',
          hint: 'Enter account number',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.number,
          textFieldController: controllers['accountNumber'],
          isRequired: true,
          readonly: hasSetupPayment,
          filled: hasSetupPayment,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
          inputFormatters: [
            LengthLimitingTextInputFormatter(10),
            FilteringTextInputFormatter.digitsOnly,
          ],
          validator: [
            Validators.minLength(10, 'Enter a valid bank account number'),
            Validators.maxLength(10, 'Enter a valid bank account number'),
          ],
        ),
        LuxTextField(
          title: 'Bank Name',
          hint: 'Enter bank name',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.text,
          textFieldController: controllers['bankName'],
          isRequired: true,
          readonly: hasSetupPayment,
          filled: hasSetupPayment,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
      ],
    );
  }

  final formKey = GlobalKey<FormState>();

  final Map<String, TextEditingController> controllers = {
    'accountName': TextEditingController(),
    'accountNumber': TextEditingController(),
    'bankName': TextEditingController(),
  };

  void handleSubmit(BuildContext context, WidgetRef ref) async {
    if (!formKey.currentState!.validate()) return;
    final data = controllers.data();

    Loader.show(context);

    final res = await ref.read(authServiceProvider).addPaymentInfo(data);
    res.when(
      (_) {
        ref.read(authControllerProvider.notifier).setPaymentInfo(PaymentInfo(
            bankName: data['bankName'],
            accountNumber: data['accountNumber'],
            accountName: data['accountName']));
        Loader.hide();
        Toast.success('The bank account was added successfully');
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }
}
