import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/auth/auth_params.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:fleet_mobile/features/auth/widgets/widgets.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:wc_form_validators/wc_form_validators.dart';

class SignUpScreen extends ConsumerStatefulWidget {
  const SignUpScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends ConsumerState<SignUpScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return FormScreen(
      formKey: formKey,
      title: 'LuxLet Manager SignUp',
      submitButtonText: 'Create Account',
      handleSubmit: () => handleSignUp(context, ref),
      body: [
        LuxTextField(
          title: 'Name',
          hint: 'firstname, middlename, lastname',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.text,
          textFieldController: controllers['name'],
          // autovalidateMode: AutovalidateMode.disabled,
          isRequired: true,
          // filled: true,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
          validator: [
            Validators.patternRegExp(RegExp(r'^\s*\S+\s+\S+.*$'),
                'Please enter at least a first name and a last name')
          ],
        ),
        LuxTextField(
          title: 'Email',
          hint: 'Enter support email',
          textFieldType: TextFieldType.email,
          keyboardType: TextInputType.emailAddress,
          textFieldController: controllers['email'],
          // autovalidateMode: AutovalidateMode.disabled,
          isRequired: true,
          // filled: true,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
        LuxTextField(
          title: 'Phone number',
          hint: 'Enter support phone',
          textFieldType: TextFieldType.phone,
          keyboardType: TextInputType.phone,
          textFieldController: controllers['phone'],
          // autovalidateMode: AutovalidateMode.disabled,
          isRequired: true,
          // filled: true,
          inputFormatters: [validNumbers()],
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
        ValueListenableBuilder(
          valueListenable: _obscurePassword,
          builder: (context, obscured, _) {
            return LuxTextField(
              title: 'Password',
              hint: 'Password',
              textFieldType: TextFieldType.password,
              keyboardType: TextInputType.visiblePassword,
              textFieldController: controllers['password'],
              // autovalidateMode: AutovalidateMode.disabled,
              isRequired: true,
              obscureText: obscured,
              // filled: true,
              onSubmitted: () {
                FocusScope.of(context).nextFocus();
              },
              suffixIcon: IconButton(
                icon: obscured == true
                    ? const Icon(Icons.visibility_outlined)
                    : const Icon(Icons.visibility_off_outlined),
                onPressed: () {
                  _obscurePassword.value = !_obscurePassword.value;
                },
              ),
            );
          },
        ),
        LuxAddressField(
          isRequired: true,
          // filled: true,
          title: 'Address',
          hint: 'Search for your address',
          controller: controllers['address'],
          onLocationSelected: (address) {
            _address.value = address;
          },
        ),
        TextButton.icon(
          onPressed: () =>
              openUri('https://www.luxletafrica.com/terms-and-conditions'),
          icon: const Icon(
            Icons.arrow_forward,
            color: Palette.kFFFFFF,
          ),
          label: Text(
            'By signing up you agree to our terms and conditions',
            style: textTheme.bodyMedium?.copyWith(
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
      footer: Padding(
        padding: const EdgeInsets.only(bottom: 20.0),
        child: TextButton(
          onPressed: () => context.pop(),
          child: Text(
            'Login',
            style: textTheme.bodyLarge?.copyWith(color: Palette.kFFB800),
          ),
        ),
      ),
    );
  }

  final formKey = GlobalKey<FormState>();

  final _obscurePassword = ValueNotifier<bool>(true);
  final _address = ValueNotifier<LuxAddress?>(null);

  final Map<String, TextEditingController> controllers = {
    'name': TextEditingController(),
    'email': TextEditingController(),
    'phone': TextEditingController(),
    'password': TextEditingController(),
    'address': TextEditingController(),
  };

  Future<void> handleSignUp(BuildContext context, WidgetRef ref) async {
    if (!formKey.currentState!.validate()) return;
    final data = controllers.data();

    Loader.show(context);

    final fingerprint = await getDeviceFingerprint();
    final fcmToken = await ref.read(pushNotificationProvider).getFcmToken();
    final authService = ref.read(authServiceProvider);
    final authNotifier = ref.read(authControllerProvider.notifier);
    final routerProvider = ref.read(routeProvider);

    final names = getNames(data['name']);

    final params = SignUpParams(
      firstName: names['firstName']!,
      middleName: names['middleName'],
      lastName: names['lastName']!,
      email: data['email'],
      phoneNumber: data['phone'],
      password: data['password'],
      deviceId: fingerprint,
      fcmToken: fcmToken,
      address: _address.value!,
    );

    final res = await authService.signUp(params);

    res.when(
      (user) async {
        await authNotifier.login(user);
        routerProvider.router.goNamed(homePath);
        Loader.hide();
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }

  Map<String, String?> getNames(String text) {
    // Split the text by spaces
    List<String> nameParts = text.trim().split(' ');

    // Prepare default values for firstName, middleName, and lastName
    String? firstName;
    String? middleName;
    String? lastName;

    // Assign values based on the number of parts
    if (nameParts.length == 1) {
      firstName = nameParts[0];
    } else if (nameParts.length == 2) {
      firstName = nameParts[0];
      lastName = nameParts[1];
    } else if (nameParts.length >= 3) {
      firstName = nameParts[0];
      lastName = nameParts.last;
      middleName = nameParts.sublist(1, nameParts.length - 1).join(' ');
    }

    // Return a map with the results
    return {
      'firstName': firstName,
      'middleName': middleName,
      'lastName': lastName,
    };
  }
}
