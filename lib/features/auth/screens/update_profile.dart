import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:fleet_mobile/features/auth/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class UpdateProfileScreen extends ConsumerStatefulWidget {
  const UpdateProfileScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _UpdateProfileScreenState();
}

class _UpdateProfileScreenState extends ConsumerState<UpdateProfileScreen> {
  @override
  void initState() {
    super.initState();
    registerControllers();
  }

  @override
  Widget build(BuildContext context) {
    return FormScreen(
      formKey: formKey,
      title: 'Update Profile',
      submitButtonText: 'Update Profile',
      handleSubmit: () => handleSubmit(context, ref),
      body: [
        LuxTextField(
          title: 'First Name',
          hint: 'Enter your first name',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.text,
          textFieldController: controllers['firstName'],
          isRequired: true,
          // filled: true,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
        LuxTextField(
          title: 'Last Name',
          hint: 'Enter your last name',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.text,
          textFieldController: controllers['lastName'],
          isRequired: true,
          // filled: true,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
        LuxTextField(
          readonly: true,
          title: 'Email',
          hint: 'Enter support email',
          textFieldType: TextFieldType.email,
          keyboardType: TextInputType.emailAddress,
          textFieldController: controllers['email'],
          isRequired: true,
          filled: true,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
        LuxTextField(
          readonly: true,
          title: 'Phone number',
          hint: 'Enter support phone',
          textFieldType: TextFieldType.phone,
          keyboardType: TextInputType.phone,
          textFieldController: controllers['phone'],
          isRequired: true,
          filled: true,
          inputFormatters: [validNumbers()],
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
      ],
      footer: const YMargin(40),
    );
  }

  final formKey = GlobalKey<FormState>();

  final Map<String, TextEditingController> controllers = {
    'firstName': TextEditingController(),
    'lastName': TextEditingController(),
    'email': TextEditingController(),
    'phone': TextEditingController(),
  };

  registerControllers() {
    Future.microtask(() {
      final user = ref.read(authControllerProvider).user;
      controllers['firstName']!.text = user!.firstName;
      controllers['lastName']!.text = user.lastName ?? '';
      controllers['email']!.text = user.email;
      controllers['phone']!.text = user.localPhone;
    });
  }

  void handleSubmit(BuildContext context, WidgetRef ref) async {
    if (!formKey.currentState!.validate()) return;
    final data = controllers.data();
    data.remove('email');
    data.remove('phone');

    final userId = ref.read(authControllerProvider).user!.id;

    Loader.show(context);

    final res =
        await ref.read(authServiceProvider).updateUserProfile(userId, data);

    res.when(
      (_) {
        Loader.hide();
        Toast.success('The profile was updated successfully');
        ref
            .read(authControllerProvider.notifier)
            .updateUserProfile(data['firstName'], data['lastName']);
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }
}
