import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_params.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:fleet_mobile/features/auth/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class ResetPasswordScreen extends ConsumerWidget {
  ResetPasswordScreen(this.authToken, {super.key});

  final String authToken;

  final formKey = GlobalKey<FormState>();
  final _obscurePassword = ValueNotifier<bool>(true);
  final Map<String, TextEditingController> controllers = {
    'newPassword': TextEditingController(),
    'confirmPassword': TextEditingController(),
  };

  Future<void> handlePasswordReset(BuildContext context, WidgetRef ref) async {
    if (!formKey.currentState!.validate()) return;

    final data = controllers.data();
    final newPassword = data['newPassword'];
    final confirmPassword = data['confirmPassword'];

    if (newPassword != confirmPassword) {
      Toast.error('Passwords do not match');
      return;
    }

    final params =
        ResetPasswordParams(newPassword: newPassword, authToken: authToken);

    Loader.show(context);

    final res = await ref.read(authServiceProvider).resetPassword(params);

    res.when(
      (_) async {
        Toast.success('Password reset was successful');
        await Future.delayed(const Duration(seconds: 3));
        if (context.mounted) context.goNamed(loginPath);
        Loader.hide();
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FormScreen(
      formKey: formKey,
      title: 'Reset Password',
      submitButtonText: 'Reset Password',
      handleSubmit: () => handlePasswordReset(context, ref),
      body: [
        ValueListenableBuilder(
          valueListenable: _obscurePassword,
          builder: (context, obscured, _) {
            return Column(
              children: [
                LuxTextField(
                  title: 'New Password',
                  hint: 'Password',
                  textFieldType: TextFieldType.password,
                  keyboardType: TextInputType.visiblePassword,
                  textFieldController: controllers['newPassword'],
                  // autovalidateMode: AutovalidateMode.disabled,
                  isRequired: true,
                  obscureText: obscured,
                  filled: true,
                  onSubmitted: () {
                    FocusScope.of(context).nextFocus();
                  },
                  suffixIcon: IconButton(
                    icon: obscured == true
                        ? const Icon(Icons.visibility_outlined)
                        : const Icon(Icons.visibility_off_outlined),
                    onPressed: () {
                      _obscurePassword.value = !_obscurePassword.value;
                    },
                  ),
                ),
                LuxTextField(
                  title: 'Confirm Password',
                  hint: 'Password',
                  textFieldType: TextFieldType.password,
                  keyboardType: TextInputType.visiblePassword,
                  textFieldController: controllers['confirmPassword'],
                  // autovalidateMode: AutovalidateMode.disabled,
                  isRequired: true,
                  obscureText: obscured,
                  filled: true,
                  onSubmitted: () {
                    FocusScope.of(context).nextFocus();
                  },
                  suffixIcon: IconButton(
                    icon: obscured == true
                        ? const Icon(Icons.visibility_outlined)
                        : const Icon(Icons.visibility_off_outlined),
                    onPressed: () {
                      _obscurePassword.value = !_obscurePassword.value;
                    },
                  ),
                )
              ],
            );
          },
        ),
      ],
    );
  }
}
