import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/auth/auth_params.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:fleet_mobile/features/auth/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ChangePasswordScreen extends ConsumerWidget {
  ChangePasswordScreen({super.key});

  final formKey = GlobalKey<FormState>();

  // Holds whether or not the password fields should be obscured
  final _obscurePassword = ValueNotifier<bool>(true);

  // Controls the form’s auto-validation state
  final _autoValidateMode =
      ValueNotifier<AutovalidateMode>(AutovalidateMode.disabled);

  // Map of controllers for convenience
  final Map<String, TextEditingController> controllers = {
    'oldPassword': TextEditingController(),
    'newPassword': TextEditingController(),
  };

  Future<void> handlePasswordReset(BuildContext context, WidgetRef ref) async {
    // 1. Validate the form first
    if (!formKey.currentState!.validate()) {
      // If invalid, set autovalidate to always so errors become visible
      _autoValidateMode.value = AutovalidateMode.always;
      return;
    }

    // 2. If valid, save the form and proceed
    formKey.currentState?.save();

    final data = controllers.data();
    final oldPassword = data['oldPassword'];
    final newPassword = data['newPassword'];

    if (oldPassword == newPassword) {
      Toast.error('Passwords can not be the same');
      return;
    }

    final params = ChangePasswordParams(
      newPassword: newPassword,
      oldPassword: oldPassword,
    );

    Loader.show(context);

    final res = await ref.read(authServiceProvider).changePassword(params);

    res.when(
      (authToken) async {
        Loader.hide();
        Toast.success('Password was changed successfully');

        // 3. Clear fields properly (disable validation, reset form, clear controllers)
        clearFields();

        // 4. Update auth state
        updateDioProvider(authToken, ref);
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }

  /// Update the dio provider with the new auth token
  void updateDioProvider(String authToken, WidgetRef ref) {
    final dio = ref.read(dioProvider);
    dio.options.headers['Authorization'] = 'Bearer $authToken';
    final user = ref.read(authControllerProvider).user;

    // Update user in state & local storage
    ref
        .read(authControllerProvider.notifier)
        .login(user!.copyWith(authToken: authToken));
  }

  /// Clears the password fields and resets the form to a pristine state
  void clearFields() {
    // Disable autovalidation first
    _autoValidateMode.value = AutovalidateMode.disabled;

    // Reset the form (removes any error states and sets fields to initialValue)
    formKey.currentState!.reset();

    // Clear the text from each controller
    controllers.forEach((key, controller) => controller.clear());
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ValueListenableBuilder<AutovalidateMode>(
      valueListenable: _autoValidateMode,
      builder: (context, autoValidateMode, _) {
        return FormScreen(
          formKey: formKey,
          title: 'Change Password',
          submitButtonText: 'Change Password',
          handleSubmit: () => handlePasswordReset(context, ref),
          autoValidateMode: autoValidateMode, // wire up to the FormScreen
          body: [
            ValueListenableBuilder(
              valueListenable: _obscurePassword,
              builder: (context, obscured, _) {
                return Column(
                  children: [
                    LuxTextField(
                      title: 'Old Password',
                      hint: 'Password',
                      textFieldType: TextFieldType.password,
                      keyboardType: TextInputType.visiblePassword,
                      textFieldController: controllers['oldPassword'],
                      isRequired: true,
                      obscureText: obscured,
                      autovalidateMode: autoValidateMode,
                      onSubmitted: () {
                        FocusScope.of(context).nextFocus();
                      },
                      suffixIcon: IconButton(
                        icon: obscured == true
                            ? const Icon(Icons.visibility_outlined)
                            : const Icon(Icons.visibility_off_outlined),
                        onPressed: () {
                          _obscurePassword.value = !_obscurePassword.value;
                        },
                      ),
                    ),
                    LuxTextField(
                      title: 'New Password',
                      hint: 'Password',
                      textFieldType: TextFieldType.password,
                      keyboardType: TextInputType.visiblePassword,
                      textFieldController: controllers['newPassword'],
                      isRequired: true,
                      obscureText: obscured,
                      autovalidateMode: autoValidateMode,
                      onSubmitted: () {
                        FocusScope.of(context).nextFocus();
                      },
                      suffixIcon: IconButton(
                        icon: obscured == true
                            ? const Icon(Icons.visibility_outlined)
                            : const Icon(Icons.visibility_off_outlined),
                        onPressed: () {
                          _obscurePassword.value = !_obscurePassword.value;
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }
}
