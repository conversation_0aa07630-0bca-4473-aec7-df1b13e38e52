import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/auth/auth_params.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:fleet_mobile/features/auth/widgets/widgets.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class LoginScreen extends ConsumerWidget {
  LoginScreen({super.key});

  final formKey = GlobalKey<FormState>();

  final Map<String, TextEditingController> controllers = {
    'username': TextEditingController(),
    'password': TextEditingController(),
  };

  final _obscurePassword = ValueNotifier<bool>(true);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return FormScreen(
      formKey: formKey,
      title: 'Login to LuxLet',
      submitButtonText: 'Login',
      handleSubmit: () => handleLogin(context, ref),
      body: [
        LuxTextField(
          title: 'Email/Phone number',
          hint: 'Enter email/phone number',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.text,
          textFieldController: controllers['username'],
          // autovalidateMode: AutovalidateMode.disabled,
          isRequired: true,
          // filled: true,
          // filledColor: Palette.k2D2C2B,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
        ValueListenableBuilder(
          valueListenable: _obscurePassword,
          builder: (context, obscured, _) {
            return LuxTextField(
              title: 'Password',
              hint: 'Password',
              textFieldType: TextFieldType.password,
              keyboardType: TextInputType.visiblePassword,
              textFieldController: controllers['password'],
              // autovalidateMode: AutovalidateMode.disabled,
              isRequired: true,
              obscureText: obscured,
              // filled: true,
              // filledColor: Palette.k2D2C2B,
              onSubmitted: () {
                FocusScope.of(context).nextFocus();
              },
              suffixIcon: IconButton(
                icon: obscured == true
                    ? const Icon(Icons.visibility_outlined)
                    : const Icon(Icons.visibility_off_outlined),
                onPressed: () {
                  _obscurePassword.value = !_obscurePassword.value;
                },
              ),
            );
          },
        ),
      ],
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: TextButton(
              onPressed: () => context.pushNamed(signUpPath),
              child: Text(
                'Manager SignUp',
                style: textTheme.bodyLarge?.copyWith(color: Palette.kFFB800),
              ),
            ),
          ),
          Expanded(
            child: TextButton(
              onPressed: () => context.pushNamed(forgotPasswordPath),
              child: Text(
                'Forgot Password?',
                style: textTheme.bodyLarge?.copyWith(color: Palette.kFFB800),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> handleLogin(BuildContext context, WidgetRef ref) async {
    if (!formKey.currentState!.validate()) return;
    final data = controllers.data();

    Loader.show(context);

    final fingerprint = await getDeviceFingerprint();
    final fcmToken = await ref.read(pushNotificationProvider).getFcmToken();
    final authService = ref.read(authServiceProvider);
    final authNotifier = ref.read(authControllerProvider.notifier);
    final routerProvider = ref.read(routeProvider);

    final params = LoginParams(
      username: data['username'],
      password: data['password'],
      deviceId: fingerprint,
      fcmToken: fcmToken,
    );

    final res = await authService.login(params);

    res.when(
      (user) async {
        await authNotifier.login(user);
        routerProvider.router.goNamed(homePath);
        Loader.hide();
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }
}
