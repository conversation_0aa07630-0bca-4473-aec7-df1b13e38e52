import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_params.dart';
import 'package:fleet_mobile/features/auth/auth_service.dart';
import 'package:fleet_mobile/features/auth/widgets/widgets.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class ForgotPasswordScreen extends ConsumerWidget {
  ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return FormScreen(
      formKey: formKey,
      title: 'Request Password Reset',
      submitButtonText: 'Reset Password',
      handleSubmit: () => handleForgotPassword(context, ref),
      body: [
        LuxTextField(
          title: 'Email/Phone number',
          hint: 'Enter email/phone number',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.text,
          textFieldController: controllers['username'],
          isRequired: true,
          // filled: true,
          // filledColor: Palette.k2D2C2B,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
        ),
      ],
      footer: TextButton(
        onPressed: () => context.pop(),
        child: Text(
          'Login',
          style: textTheme.bodyLarge?.copyWith(color: Palette.kFFB800),
        ),
      ),
    );
  }

  final formKey = GlobalKey<FormState>();

  final Map<String, TextEditingController> controllers = {
    'username': TextEditingController(),
  };

  Future<void> handleForgotPassword(BuildContext context, WidgetRef ref) async {
    if (!formKey.currentState!.validate()) return;
    final data = controllers.data();

    final params = EmailOrPhoneParams(username: data['username']);

    Loader.show(context);

    final res = await ref.read(authServiceProvider).sendOtp(params);

    res.when(
      (_) {
        context.pushNamed(verifyOtpPath, extra: params);
        Loader.hide();
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }
}
