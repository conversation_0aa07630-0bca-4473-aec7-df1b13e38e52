import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'auth_service.dart';
import 'auth_state.dart';

final authControllerProvider = StateNotifierProvider<AuthController, AuthState>(
  (ref) {
    final authService = ref.watch(authServiceProvider);
    return AuthController(
      const AuthState(),
      authService,
    );
  },
);

class AuthController extends StateNotifier<AuthState> {
  AuthController(
    super.state,
    this._authService,
  );

  final AuthService _authService;

  Future<void> login(User user) async {
    state = state.copyWith(
      user: user,
    );
    await _saveUser(user);
    return;
  }

  void setPaymentInfo(PaymentInfo paymentInfo) {
    state =
        state.copyWith(user: state.user?.copyWith(paymentInfo: paymentInfo));
  }

  void updateUserProfile(String firstName, String lastName) {
    state = state.copyWith(
        user: state.user?.copyWith(firstName: firstName, lastName: lastName));
  }

  Future<void> logout() async {
    state = const AuthState();
    return _deleteUser();
  }

  Future<void> fetchWallet() async {
    int retryCount = 0;
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 5);

    while (retryCount < maxRetries) {
      final res = await _authService.fetchWallet();
      res.when(
        (wallet) {
          state = state.copyWith(user: state.user?.copyWith(wallet: wallet));
          return;
        },
        (error) {
          retryCount++;
          if (retryCount >= maxRetries) {
            // Handle the error after max retries
            return;
          }
        },
      );
      await Future.delayed(retryDelay);
    }
  }

  void updateUser(User user) {
    state = state.copyWith(user: user);
    _saveUser(user);
  }

  /// Refresh user data from the server
  Future<void> refreshUserData() async {
    try {
      final res = await _authService.getUser();
      res.when(
        (user) {
          updateUser(user);
        },
        (error) {
          // Silently fail - don't show error to user for background refresh
          debugPrint('Failed to refresh user data: ${error.message}');
        },
      );
    } catch (e) {
      debugPrint('Error refreshing user data: $e');
    }
  }

  Future<void> _saveUser(User user) async {
    final st = FlutterSecureStorage(aOptions: getAndroidOptions());
    return await st.write(key: Keys.user, value: user.toJson());
  }

  Future<void> _deleteUser() async {
    final st = FlutterSecureStorage(aOptions: getAndroidOptions());
    return await st.delete(key: Keys.user);
  }

  @override
  void dispose() {
    // dispose;
    super.dispose();
  }
}
