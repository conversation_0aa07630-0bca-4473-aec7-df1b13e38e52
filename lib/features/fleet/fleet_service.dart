import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/fleet/fleet_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'fleet_params.dart';

final fleetServiceProvider = Provider<FleetService>((ref) {
  final fleetRepository = ref.watch(fleetRepositoryProvider);
  return LuxFleetService(
    fleetRepository,
  );
});

abstract class FleetService {
  Future<Result<Car, Failure>> getCar(String carId);
  Future<Result<List<Car>, Failure>> getCars();
  Future<Result<Car, Failure>> createCar(Map<String, dynamic> params);
  Future<Result<BrandAndCategoryResult, Failure>> getBrandsAndCategories();
  Future<Result<Unit, Failure>> updateCar(UpdateCarParams params);
  Future<Result<Unit, Failure>> delistCar(String carId);
  Future<Result<Unit, Failure>> updateImageVisibility(
      ImageVisibilityParams params);
  Future<Result<Unit, Failure>> changeMainImage(String carId, String imageId);
  Future<Result<Unit, Failure>> deleteImage(String carId, String imageId);
}

class LuxFleetService implements FleetService {
  final FleetRepository _fleetRepository;

  LuxFleetService(this._fleetRepository);

  @override
  Future<Result<Car, Failure>> createCar(Map<String, dynamic> params) async {
    try {
      final res = await _fleetRepository.createCar(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<BrandAndCategoryResult, Failure>>
      getBrandsAndCategories() async {
    try {
      final res = await _fleetRepository.getBrandsAndCategories();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Car, Failure>> getCar(String carId) async {
    try {
      final res = await _fleetRepository.getCar(carId);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<List<Car>, Failure>> getCars() async {
    try {
      final res = await _fleetRepository.getCars();
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> updateCar(UpdateCarParams params) async {
    try {
      final res = await _fleetRepository.updateCar(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> delistCar(String carId) async {
    try {
      final res = await _fleetRepository.delistCar(carId);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> updateImageVisibility(
      ImageVisibilityParams params) async {
    try {
      final res = await _fleetRepository.updateImageVisibility(params);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> changeMainImage(
      String carId, String imageId) async {
    try {
      final res = await _fleetRepository.changeMainImage(carId, imageId);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Unit, Failure>> deleteImage(
      String carId, String imageId) async {
    try {
      final res = await _fleetRepository.deleteImage(carId, imageId);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }
}
