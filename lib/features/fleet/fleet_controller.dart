import 'dart:async';

import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/fleet/fleet_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'fleet_params.dart';
import 'fleet_state.dart';

final fleetControllerProvider =
    StateNotifierProvider<FleetController, FleetState>(
  (ref) {
    final fleetService = ref.watch(fleetServiceProvider);
    return FleetController(
      FleetState.initial(),
      fleetService,
    );
  },
);

class FleetController extends StateNotifier<FleetState> {
  FleetController(
    super.state,
    this._fleetService,
  );

  final FleetService _fleetService;

  void addCar(Car car) {
    final cars = [...state.cars.value!, car];
    state = state.copyWith(cars: AsyncValue.data(cars), carInView: car);
  }

  void updateCarInView(Car car) {
    state = state.copyWith(carInView: car);
  }

  void updateCarInfo({
    String? carId,
    LuxAddress? address,
    num? dailyMinPrice,
    Schedule? schedule,
  }) {
    final index = state.cars.value!.indexWhere((x) => x.id == carId);
    if (index != -1) {
      final updatedCars = List<Car>.from(state.cars.value!);
      updatedCars[index] = updatedCars[index].copyWith(
          address: address, dailyMinPrice: dailyMinPrice, schedule: schedule);

      state = state.copyWith(cars: AsyncValue.data(updatedCars));
    }
  }

  void updateImageVisibility(String carId, String imageId, bool isVisible) {
    // Find the index of the car based on carId
    final carIndex = state.cars.value?.indexWhere((car) => car.id == carId);

    // Ensure the car exists
    if (carIndex == null || carIndex == -1) {
      return;
    }

    // Create a new list of cars to maintain immutability
    final updatedCars = List<Car>.from(state.cars.value!);

    // Find the specific car's images and update visibility for the target image
    final updatedImages = updatedCars[carIndex].images.map((image) {
      if (image.id == imageId) {
        // Return a copy with updated visibility
        return image.copyWith(isVisible: isVisible);
      }
      return image;
    }).toList();

    // Update the specific car with the new list of images
    updatedCars[carIndex] =
        updatedCars[carIndex].copyWith(images: updatedImages);

    // Update the state with the new cars list
    state = state.copyWith(cars: AsyncValue.data(updatedCars));
  }

  Future<void> loadCars([int retry = 3]) async {
    state = state.copyWith(cars: const AsyncValue.loading());

    final res = await _fleetService.getCars();
    res.when(
      (cars) {
        state = state.copyWith(cars: AsyncValue.data(cars));
      },
      (error) async {
        if (retry > 0) {
          await Future.delayed(
              const Duration(milliseconds: retryDelayMilliseconds));
          loadCars(retry - 1);
        } else {
          state = state.copyWith(
            cars: AsyncValue.error(error, StackTrace.current),
          );
        }
      },
    );
  }

  Future<void> getBrandsAndCategories([int retry = 3]) async {
    final hasPopulatedBrands =
        state.brands is AsyncData && state.brands.value!.isNotEmpty;
    final hasPopulatedCategories =
        state.categories is AsyncData && state.categories.value!.isNotEmpty;

    if (hasPopulatedBrands && hasPopulatedCategories) return;

    state = state.copyWith(
      brands: const AsyncValue.loading(),
      categories: const AsyncValue.loading(),
    );

    final res = await _fleetService.getBrandsAndCategories();

    res.when(
      (data) {
        state = state.copyWith(
          brands: AsyncValue.data(data.brands),
          categories: AsyncValue.data(data.categories),
        );
      },
      (error) async {
        if (retry > 0) {
          await Future.delayed(
              const Duration(milliseconds: retryDelayMilliseconds));
          getBrandsAndCategories(retry - 1);
        } else {
          state = state.copyWith(
            brands: AsyncValue.error(error, StackTrace.current),
            categories: AsyncValue.error(error, StackTrace.current),
          );
        }
      },
    );
  }

  Future<Result<Unit, Failure>> delistCar(String carId) async {
    final res = await _fleetService.delistCar(carId);
    return res.when(
      (unit) {
        final cars = state.cars.value!.where((x) => x.id != carId).toList();
        state = state.copyWith(cars: AsyncValue.data(cars));
        return Success(unit);
      },
      (error) {
        return Error(error);
      },
    );
  }

  Future<Result<Unit, Failure>> updateAvailability(String carId, bool value,
      [int retry = 3]) async {
    final params = UpdateCarParams(carId: carId, data: {'isAvailable': value});
    final res = await _fleetService.updateCar(params);
    return res.when(
      (unit) {
        final index = state.cars.value!.indexWhere((x) => x.id == carId);
        if (index != -1) {
          final updatedCars = List<Car>.from(state.cars.value!);
          updatedCars[index] = updatedCars[index].copyWith(isAvailable: value);

          state = state.copyWith(cars: AsyncValue.data(updatedCars));
        }
        return Success(unit);
      },
      (error) async {
        if (retry > 0) {
          await Future.delayed(
              const Duration(milliseconds: retryDelayMilliseconds));
          return updateAvailability(carId, value, retry - 1);
        } else {
          return Error(error);
        }
      },
    );
  }

  Future<void> runImageJob(
    String carId, {
    int intervalSeconds = 5,
    int maxRetries = 10,
  }) async {
    int retryCount = 0;

    Timer.periodic(Duration(seconds: intervalSeconds), (Timer timer) async {
      final res = await _fleetService.getCar(carId);

      res.when(
        (car) {
          final cars = List<Car>.from(state.cars.value ?? []);

          final carIndex = cars.indexWhere((x) => x.id == carId);

          // If car is found, proceed
          if (carIndex != -1) {
            final imagesInState = cars[carIndex].images;
            final updatedImages = car.images;

            // Check if there are new images, then update the state
            if (updatedImages.length > imagesInState.length) {
              cars[carIndex] = cars[carIndex].copyWith(images: updatedImages);
              state = state.copyWith(cars: AsyncValue.data(cars));

              if (state.carInView?.id == carId) {
                state = state.copyWith(
                  carInView: state.carInView!.copyWith(images: updatedImages),
                );
              }

              // Reset retry count on success
              retryCount = 0;
              timer.cancel();
            } else {
              // No new images, increment retry count
              retryCount++;

              // If max retries are reached, stop the job
              if (retryCount >= maxRetries) {
                timer.cancel();
              }
            }
          } else {
            // If car is not found, stop the timer
            timer.cancel();
          }
        },
        (error) {
          // Error handling, increment retry count
          retryCount++;

          // If max retries are reached, stop the job
          if (retryCount >= 3) {
            timer.cancel();
          }
        },
      );
    });
  }

  void deleteCarImage(String carId, String imageId) {
    final index = state.cars.value!.indexWhere((x) => x.id == carId);
    if (index != -1) {
      final updatedCars = List<Car>.from(state.cars.value!);
      final updatedImages =
          updatedCars[index].images.where((x) => x.id != imageId).toList();
      updatedCars[index] = updatedCars[index].copyWith(images: updatedImages);

      state = state.copyWith(cars: AsyncValue.data(updatedCars));

      if (state.carInView?.id == carId) {
        state = state.copyWith(
          carInView: state.carInView!.copyWith(images: updatedImages),
        );
      }
    }
  }

  void setMainImage(String carId, String imageId) {
    final index = state.cars.value!.indexWhere((x) => x.id == carId);
    if (index != -1) {
      final updatedCars = List<Car>.from(state.cars.value!);
      final updatedImages = updatedCars[index].images.map((x) {
        if (x.id == imageId) {
          return x.copyWith(isMain: true);
        }

        return x.copyWith(isMain: false);
      }).toList();

      updatedCars[index] = updatedCars[index].copyWith(images: updatedImages);

      state = state.copyWith(cars: AsyncValue.data(updatedCars));

      if (state.carInView?.id == carId) {
        state = state.copyWith(
          carInView: state.carInView!.copyWith(images: updatedImages),
        );
      }
    }
  }

  @override
  void dispose() {
    // dispose;
    super.dispose();
  }
}
