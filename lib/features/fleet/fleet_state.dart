import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart' hide Category;
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:fleet_mobile/core/core.dart';

@immutable
class FleetState extends Equatable {
  final AsyncValue<List<Car>> cars;
  final AsyncValue<List<Brand>> brands;
  final AsyncValue<List<Category>> categories;
  final Car? carInView;

  const FleetState({
    required this.cars,
    required this.brands,
    required this.categories,
    this.carInView,
  });

  factory FleetState.initial() {
    return const FleetState(
      cars: AsyncValue.data([]),
      brands: AsyncValue.data([]),
      categories: AsyncValue.data([]),
      carInView: null,
    );
  }

  FleetState copyWith({
    AsyncValue<List<Car>>? cars,
    AsyncValue<List<Brand>>? brands,
    AsyncValue<List<Category>>? categories,
    Car? carInView,
  }) {
    return FleetState(
      cars: cars ?? this.cars,
      brands: brands ?? this.brands,
      categories: categories ?? this.categories,
      carInView: carInView ?? this.carInView,
    );
  }

  @override
  List<Object?> get props => [cars, brands, categories, carInView];
}
