import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:fleet_mobile/core/core.dart';

class BrandAndCategoryResult {
  final List<Brand> brands;
  final List<Category> categories;

  BrandAndCategoryResult({
    required this.brands,
    required this.categories,
  });

  BrandAndCategoryResult copyWith({
    List<Brand>? brands,
    List<Category>? categories,
  }) {
    return BrandAndCategoryResult(
      brands: brands ?? this.brands,
      categories: categories ?? this.categories,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'brands': brands.map((x) => x.toMap()).toList(),
      'categories': categories.map((x) => x.toMap()).toList(),
    };
  }

  factory BrandAndCategoryResult.fromMap(Map<String, dynamic> map) {
    return BrandAndCategoryResult(
      brands: List<Brand>.from(map['brands']?.map((x) => Brand.fromMap(x))),
      categories: List<Category>.from(
          map['categories']?.map((x) => Category.fromMap(x))),
    );
  }

  String toJson() => json.encode(toMap());

  factory BrandAndCategoryResult.fromJson(String source) =>
      BrandAndCategoryResult.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}

class CarResult {
  final List<Car> cars;
  final QueryParameters queryParams;

  CarResult({
    required this.cars,
    required this.queryParams,
  });

  CarResult copyWith({
    List<Car>? cars,
    QueryParameters? queryParams,
  }) {
    return CarResult(
      cars: cars ?? this.cars,
      queryParams: queryParams ?? this.queryParams,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'cars': cars.map((x) => x.toMap()).toList(),
      'queryParams': queryParams.toMap(),
    };
  }

  factory CarResult.fromMap(Map<String, dynamic> map) {
    return CarResult(
      cars: List<Car>.from(map['cars']?.map((x) => Car.fromMap(x))),
      queryParams: QueryParameters.fromMap(map['queryParams']),
    );
  }

  String toJson() => json.encode(toMap());

  factory CarResult.fromJson(String source) =>
      CarResult.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}

class QueryParameters implements Equatable {
  final int count;
  final int totalCount;
  final int filteredDocumentsCount;
  final int page;
  final int limit;
  final dynamic prevPage;
  final dynamic nextPage;

  QueryParameters({
    required this.count,
    required this.totalCount,
    required this.filteredDocumentsCount,
    required this.page,
    required this.limit,
    required this.prevPage,
    required this.nextPage,
  });

  QueryParameters copyWith({
    int? count,
    int? totalCount,
    int? filteredDocumentsCount,
    int? page,
    int? limit,
    dynamic prevPage,
    dynamic nextPage,
  }) {
    return QueryParameters(
      count: count ?? this.count,
      totalCount: totalCount ?? this.totalCount,
      filteredDocumentsCount:
          filteredDocumentsCount ?? this.filteredDocumentsCount,
      page: page ?? this.page,
      limit: limit ?? this.limit,
      prevPage: prevPage ?? this.prevPage,
      nextPage: nextPage ?? this.nextPage,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'count': count,
      'totalCount': totalCount,
      'filterdDocumentsCount': filteredDocumentsCount,
      'page': page,
      'limit': limit,
      'prevPage': prevPage,
      'nextPage': nextPage,
    };
  }

  factory QueryParameters.fromMap(Map<String, dynamic> map) {
    return QueryParameters(
      count: map['count']?.toInt() ?? 0,
      totalCount: map['totalCount']?.toInt() ?? 0,
      filteredDocumentsCount: map['filterdDocumentsCount']?.toInt() ?? 0,
      page: map['page']?.toInt() ?? 0,
      limit: map['limit']?.toInt() ?? 0,
      prevPage: map['prevPage'],
      nextPage: map['nextPage'],
    );
  }

  String toJson() => json.encode(toMap());

  factory QueryParameters.fromJson(String source) =>
      QueryParameters.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';

  @override
  List<Object?> get props => [
        count,
        totalCount,
        filteredDocumentsCount,
        page,
        limit,
        prevPage,
        nextPage,
      ];

  @override
  bool? get stringify => true;
}

class UpdateCarParams {
  final Map<String, dynamic> data;
  final String carId;
  UpdateCarParams({
    required this.data,
    required this.carId,
  });
}

class ImageVisibilityParams {
  final String imageId;
  final bool isVisible;

  ImageVisibilityParams({
    required this.imageId,
    required this.isVisible,
  });

  ImageVisibilityParams copyWith({
    String? imageId,
    bool? isVisible,
  }) {
    return ImageVisibilityParams(
      imageId: imageId ?? this.imageId,
      isVisible: isVisible ?? this.isVisible,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'imageId': imageId,
      'isVisible': isVisible,
    };
  }

  factory ImageVisibilityParams.fromMap(Map<String, dynamic> map) {
    return ImageVisibilityParams(
      imageId: map['imageId'] ?? '',
      isVisible: map['isVisible'] ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  factory ImageVisibilityParams.fromJson(String source) =>
      ImageVisibilityParams.fromMap(json.decode(source));

  @override
  String toString() => '${toMap()}';
}
