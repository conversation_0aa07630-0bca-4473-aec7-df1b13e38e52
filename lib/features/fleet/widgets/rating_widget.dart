import 'package:flutter/material.dart';

class RatingWidget extends StatelessWidget {
  final int rating;
  final int maxRating;

  const RatingWidget({
    super.key,
    required this.rating,
    this.maxRating = 5,
  });

  @override
  Widget build(BuildContext context) {
    // Ensure rating doesn't exceed maxRating
    int activeStars = rating.clamp(0, maxRating);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(maxRating, (index) {
        return Icon(
          Icons.star,
          color: index < activeStars ? Colors.yellow[700] : Colors.grey,
        );
      }),
    );
  }
}
