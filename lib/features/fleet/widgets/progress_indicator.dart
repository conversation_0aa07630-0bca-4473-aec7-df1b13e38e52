import 'package:flutter/material.dart';

class CustomProgressIndicator extends StatelessWidget {
  const CustomProgressIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 8.0,
      width: 220,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50.0),
        gradient: const LinearGradient(
          colors: [
            Color(0xFFDBC474),
            Color(0xFFA07A4C),
            Color(0xFFD9AC5E),
            Color(0xFFDBC474),
            Color(0xFFDBAF5D),
            Color(0xFFAE824C),
          ],
          stops: [
            0.0,
            0.2,
            0.4,
            0.6,
            0.8,
            1.0,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
      child: const LinearProgressIndicator(
        value: 0.5, // Set your desired progress value here (0.0 to 1.0)
        valueColor: AlwaysStoppedAnimation<Color>(Colors.transparent),
        backgroundColor: Colors.transparent,
      ),
    );
  }
}
