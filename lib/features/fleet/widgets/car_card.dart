import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CarCard extends ConsumerWidget {
  CarCard(
    this.car, {
    super.key,
    this.home = false,
  });

  final Car car;
  final bool home;
  final _loading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final isLoading = ref.watch(fleetControllerProvider).cars.isLoading;
    final size = MediaQuery.of(context).size;

    return GestureDetector(
      onTap: () {
        ref.read(fleetControllerProvider.notifier).updateCarInView(car);
        context.pushNamed(carDetailPath);
      },
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8,
            ),
            child: ValueListenableBuilder<bool>(
              valueListenable: _loading,
              builder: (context, loading, _) {
                return Stack(
                  children: [
                    Skeletonizer(
                      enabled: loading || isLoading,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardTheme.color,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.secondary,
                            width: 1.5,
                          ),
                          // boxShadow: [
                          //   BoxShadow(
                          //     color: Theme.of(context).brightness ==
                          //             Brightness.light
                          //         ? Theme.of(context)
                          //             .colorScheme
                          //             .secondary
                          //             .withValues(alpha: 30)
                          //         : Theme.of(context)
                          //             .colorScheme
                          //             .secondary
                          //             .withValues(alpha: 77),
                          //     blurRadius: Theme.of(context).brightness ==
                          //             Brightness.light
                          //         ? 4
                          //         : 8,
                          //     spreadRadius: Theme.of(context).brightness ==
                          //             Brightness.light
                          //         ? 0
                          //         : 2,
                          //     offset: const Offset(0, 2),
                          //   ),
                          // ],
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              car.name.toUpperCase(),
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: Theme.of(context).colorScheme.onSurface,
                                letterSpacing: 0.5,
                              ),
                            ),
                            const YMargin(8),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Skeleton.shade(
                                  child: RenderCarImage(
                                    isLoading ? Car.defaultValue() : car,
                                    size: size,
                                  ),
                                ),
                                const XMargin(20),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const YMargin(6),
                                      Row(
                                        children: [
                                          Text(
                                            'Daily Price: ',
                                            style: textTheme.bodyMedium?.copyWith(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onSurface
                                                // .withValues(
                                                //     alpha:
                                                //         204), // ~0.8 opacity
                                                ),
                                          ),
                                          Expanded(
                                            child: Text(
                                              CurrencyItem
                                                  .formatAmountWithSymbol(
                                                context,
                                                car.dailyPrice,
                                              ),
                                              style: textTheme.bodyMedium
                                                  ?.copyWith(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .tertiary,
                                                fontWeight: FontWeight.w600,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const YMargin(4),
                                      Text(
                                        'Bookings: ${car.bookingCount}',
                                        style: textTheme.bodyMedium?.copyWith(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                            // .withValues(
                                            //     alpha: 204), // ~0.8 opacity
                                            ),
                                      ),
                                      if (!home) ...[
                                        const YMargin(12),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 12,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: car.isAvailable
                                                ? Colors.green
                                                    .withValues(alpha: 0.2)
                                                : Colors.red
                                                    .withValues(alpha: 0.2),
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            car.isAvailable
                                                ? 'Available'
                                                : 'Inactive',
                                            style:
                                                textTheme.bodyMedium?.copyWith(
                                              color: car.isAvailable
                                                  ? Colors.green
                                                  : Colors.red,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          if (home)
            Positioned(
              right: 30,
              top: 99,
              child: Skeleton.shade(
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).brightness == Brightness.light
                            ? Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withValues(alpha: 30)
                            : Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withValues(alpha: 77),
                        blurRadius:
                            Theme.of(context).brightness == Brightness.light
                                ? 4
                                : 12,
                        spreadRadius:
                            Theme.of(context).brightness == Brightness.light
                                ? 0
                                : 1,
                        offset: const Offset(1, 1),
                      ),
                    ],
                  ),
                  child: SvgPicture.asset(
                    '$svgDir/cursor.svg',
                    colorFilter: ColorFilter.mode(
                        Theme.of(context).colorScheme.onSecondary,
                        BlendMode.srcIn),
                    height: 32,
                    width: 32,
                  ),
                ),
              ),
            ),
          if (!home)
            Positioned(
              right: 5,
              top: 90,
              child: Builder(builder: (context) {
                return SizedBox(
                  width: 36,
                  height: 20,
                  child: Transform.scale(
                    scaleX: 0.8,
                    scaleY: 0.8,
                    child: CupertinoSwitch(
                      value: car.isAvailable,
                      onChanged: (value) async {
                        if (_loading.value) return;

                        _loading.value = true;
                        final res = await ref
                            .read(fleetControllerProvider.notifier)
                            .updateAvailability(car.id, value);
                        res.when(
                          (_) {
                            Toast.success('Update was successful');
                            _loading.value = false;
                          },
                          (error) {
                            Toast.error(error.message);
                            _loading.value = false;
                          },
                        );
                      },
                      activeTrackColor: Colors.green,
                      thumbColor: Colors.white,
                      inactiveTrackColor: Colors.grey.shade600,
                    ),
                  ),
                );
              }),
            ),
        ],
      ),
    );
  }
}

class RenderCarImage extends StatelessWidget {
  const RenderCarImage(
    this.car, {
    super.key,
    required this.size,
    this.heroTag,
  });

  final Car car;
  final Size size;
  final String? heroTag;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return ClipRRect(
      borderRadius: BorderRadius.circular(15),
      child: car.imageUrl == null
          ? Container(
              width: size.width * 0.35,
              height: size.width * 0.25,
              decoration: BoxDecoration(
                border: Border.all(
                    color: colorScheme.onSurface
                        .withValues(alpha: 77)), // ~0.3 opacity
                borderRadius: BorderRadius.circular(15),
                color:
                    colorScheme.surface.withValues(alpha: 128), // ~0.5 opacity
              ),
              child: Center(
                child: Icon(
                  Icons.directions_car,
                  color: colorScheme.onSurface,
                  size: 40,
                ),
              ),
            )
          : Hero(
              tag: heroTag ?? 'car_list_${car.id}',
              child: Container(
                width: size.width * 0.35,
                height: size.width * 0.25,
                decoration: BoxDecoration(
                  border: Border.all(
                      color: colorScheme.onSurface
                          .withValues(alpha: 77)), // ~0.3 opacity
                  borderRadius: BorderRadius.circular(15),
                  image: DecorationImage(
                    fit: BoxFit.cover,
                    image: NetworkImage(car.imageUrl!),
                  ),
                ),
              ),
            ),
    );
  }
}
