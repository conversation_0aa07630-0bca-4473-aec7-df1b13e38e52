import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/material.dart';

class SelectWidget<T> extends StatefulWidget {
  final String? title;
  final List<T> options;
  final String Function(T) displayString;
  final void Function(T) onSelect;
  final String? hint;
  final bool filter;
  final String? filterLabel;
  final bool filled;
  final bool isRequired;
  final TextEditingController? controller;

  const SelectWidget({
    this.title,
    required this.options,
    required this.displayString,
    required this.onSelect,
    this.hint,
    this.filter = false,
    this.filterLabel,
    this.filled = false,
    this.isRequired = false,
    this.controller,
    super.key,
  });

  @override
  SelectWidgetState<T> createState() => SelectWidgetState<T>();
}

class SelectWidgetState<T> extends State<SelectWidget<T>> {
  T? selectedItem;
  late List<T> filteredOptions;

  void _openBottomSheet() {
    setState(() {
      filteredOptions = widget.options;
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: widget.filter,
      enableDrag: true,
      useSafeArea: true,
      useRootNavigator: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      builder: (context) {
        return FractionallySizedBox(
          heightFactor: 0.9,
          child: StatefulBuilder(
            builder: (context, buildState) {
              void filterOptions(String query) {
                buildState(() {
                  filteredOptions = widget.options
                      .where((option) => widget
                          .displayString(option)
                          .toLowerCase()
                          .contains(query.toLowerCase()))
                      .toList();
                });
              }

              return Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.title != null) ...[
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            widget.title!,
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        ),
                        const Divider(),
                      ],
                      if (widget.filter)
                        TextField(
                          style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface),
                          decoration: InputDecoration(
                            hintText: widget.filterLabel ?? 'Filter Options',
                            hintStyle: TextStyle(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withValues(alpha: 128)),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withValues(alpha: 128)),
                            ),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(
                                  color:
                                      Theme.of(context).colorScheme.secondary),
                            ),
                          ),
                          onChanged: filterOptions,
                        ),
                      const SizedBox(height: 10),
                      Flexible(
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: filteredOptions.length,
                          itemBuilder: (context, index) {
                            final item = filteredOptions[index];
                            return ListTile(
                              title: Text(
                                widget.displayString(item),
                                style: TextStyle(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                              ),
                              onTap: () {
                                setState(() {
                                  selectedItem = item;
                                });
                                widget.onSelect(item);
                                Navigator.pop(context);
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return LuxTextField(
      readonly: true,
      title: widget.title,
      hint: widget.hint ?? 'Select Option',
      textFieldType: TextFieldType.text,
      keyboardType: TextInputType.text,
      isRequired: widget.isRequired,
      filled: widget.filled,
      onTap: _openBottomSheet,
      onSubmitted: () {
        FocusScope.of(context).nextFocus();
      },
      textFieldController: widget.controller,
      suffixIcon: Icon(Icons.keyboard_arrow_down, color: theme.hintColor),
    );
  }
}
