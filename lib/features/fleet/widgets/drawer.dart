import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class CustomDrawer extends ConsumerWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    final user = ref.watch(authControllerProvider).user!;

    return Drawer(
      backgroundColor: Theme.of(context).colorScheme.surface,
      child: ListView(
        padding: EdgeInsets.zero,
        children: <Widget>[
          DrawerHeader(
            child: Row(
              children: <Widget>[
                Image.asset(
                  kImgBrandAdaptiveIcon,
                  height: 80,
                ),
                const XMargin(20),
                Text(
                  'Hi ${user.firstName}',
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
          DrawerItem(
            icon: Icons.dashboard_outlined,
            title: 'Home',
            onTap: () {
              Navigator.pop(context);
              context.goNamed(homePath);
            },
          ),
          Visibility(
            visible: user.isAdmin || user.isSuperAdmin,
            child: DrawerItem(
              icon: Icons.admin_panel_settings_outlined,
              title: 'Admin Dashboard',
              onTap: () => context.pushNamed(adminDashboardPath),
            ),
          ),
          DrawerItem(
            icon: Icons.notes_outlined,
            title: 'Bookings',
            onTap: () => context.pushNamed(bookingsPath),
          ),
          DrawerItem(
            icon: Icons.local_taxi_outlined,
            title: 'Cars',
            onTap: () => context.pushNamed(carListingPath),
          ),
          DrawerItem(
            icon: Icons.account_balance_wallet_outlined,
            title: 'Payments',
            // trailing: user.hasSetupPayment,
            onTap: () {
              // if (user.hasSetupPayment) return;
              context.pushNamed(addBankAccountPath);
            },
          ),
          DrawerItem(
            icon: Icons.person_outline,
            title: 'Update Profile',
            onTap: () => context.pushNamed(updateProfilePath),
          ),
          DrawerItem(
            icon: Icons.password,
            title: 'Change Password',
            onTap: () => context.pushNamed(changePasswordPath),
          ),
          DrawerItem(
            icon: Icons.logout,
            title: 'Logout',
            onTap: () => handleLogout(context, ref),
          ),
        ],
      ),
    );
  }

  void handleLogout(BuildContext context, WidgetRef ref) {
    final authNotifier = ref.read(authControllerProvider.notifier);

    showConfirmationDialog(
      context,
      'You\'re about to logout',
      'Are you sure?',
      'Logout',
      actionTextColor: Colors.red,
      onConfirm: () {
        authNotifier.logout();
        Navigator.of(context).pop();
        context.goNamed(loginPath);
      },
    );
  }
}

class DrawerItem extends StatelessWidget {
  const DrawerItem({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.trailing = false,
  });

  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool? trailing;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return ListTile(
      leading: Icon(icon, color: colorScheme.onSurface),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
      ),
      trailing: trailing!
          ? Icon(Icons.done_all, color: colorScheme.onSurface)
          : const SizedBox.shrink(),
      onTap: onTap,
    );
  }
}
