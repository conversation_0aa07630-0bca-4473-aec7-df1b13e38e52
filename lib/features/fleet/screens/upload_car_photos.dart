import 'dart:io';
import 'dart:ui' as ui;

import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:fleet_mobile/features/fleet/screens/screens.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';

class UploadCarPhotosScreen extends ConsumerStatefulWidget {
  const UploadCarPhotosScreen(this.carId, {super.key});

  final String carId;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _UploadCarPhotosState();
}

class _UploadCarPhotosState extends ConsumerState<UploadCarPhotosScreen> {
  final picker = ImagePicker();
  List<Map<String, dynamic>> _selectedImages = [];
  List<XFile> _images = [];
  bool _isLoading = false;
  bool _isUploading = false;
  double _uploadProgress = 0.0;
  String mainImageId = '';
  late int maxImageLength;

  // Enhanced constants for high-quality image handling
  static const double maxImageSizeInMB = 5.0; // Updated to match backend (5MB)
  static const int recommendedMinWidth = 800; // Minimum recommended width
  static const int recommendedMinHeight = 600; // Minimum recommended height

  @override
  void initState() {
    int imageLength =
        ref.read(fleetControllerProvider).carInView?.images.length ?? 0;
    maxImageLength = maxImageCount - imageLength;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final mediaQuery = MediaQuery.sizeOf(context);

    final canPop = context.canPop(); // cannot pop from create car flow

    return SafeArea(
      child: PopScope(
        canPop: canPop && !_isUploading,
        onPopInvokedWithResult: (_, __) {
          if (_isUploading) {
            Toast.error('Please wait for the upload to complete');
          } else if (!canPop) {
            Toast.error('You need to upload at least one photo for this car');
          }
        },
        child: Scaffold(
          body: CustomScrollView(
            slivers: [
              if (canPop) const SliverAppBar(leading: BackButton()),
              SliverFillRemaining(
                hasScrollBody: false,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const YMargin(18),
                      Text(
                        'Upload Car Pictures',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const YMargin(10),
                      Text(
                        "Upload high-quality photos to showcase your car's best features. Our system automatically optimizes images for crisp, professional display across all devices.",
                        style: textTheme.bodyMedium,
                      ),
                      const YMargin(8),
                      // Enhanced image quality guidelines
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Palette.kFFB800.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Palette.kFFB800.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.tips_and_updates,
                                  size: 16,
                                  color: Palette.kFFB800,
                                ),
                                const XMargin(4),
                                Text(
                                  'Photo Tips for Best Results:',
                                  style: textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Palette.kFFB800,
                                  ),
                                ),
                              ],
                            ),
                            const YMargin(4),
                            Text(
                              '• Use good lighting (natural light preferred)\n'
                              '• Minimum ${recommendedMinWidth}x${recommendedMinHeight}px resolution\n'
                              '• Clean car exterior and interior\n'
                              '• Multiple angles: front, back, sides, interior',
                              style: textTheme.bodySmall?.copyWith(
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const YMargin(20),
                      _selectedImages.isNotEmpty
                          ? _buildImagePreview(mediaQuery, canPop)
                          : _buildPlaceholder(),
                      const YMargin(30),
                      GestureDetector(
                        onTap: pickGalleryImages,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.add_photo_alternate,
                              color: Palette.kFFB800,
                            ),
                            const XMargin(8),
                            Text(
                              'Select Car Pictures',
                              style: textTheme.bodyLarge?.copyWith(
                                color: Palette.kFFB800,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                        ).copyWith(top: 20),
                        child: Text(
                          'Add up to $maxImageLength photos. Each photo must be ${maxImageSizeInMB.toInt()}MB max.\n'
                          'High-quality images (${recommendedMinWidth}x${recommendedMinHeight}px+) recommended.',
                          textAlign: TextAlign.center,
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                        ),
                      ),
                      const Spacer(),
                      const YMargin(30),
                      Center(
                        child: _isUploading
                            ? Column(
                                children: [
                                  Container(
                                    width: 320,
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color:
                                          Theme.of(context).colorScheme.surface,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondary
                                            .withAlpha(51),
                                      ),
                                    ),
                                    child: Column(
                                      children: [
                                        Text(
                                          'Uploading Images...',
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                        const YMargin(12),
                                        LinearProgressIndicator(
                                          value: _uploadProgress,
                                          backgroundColor: Theme.of(context)
                                              .colorScheme
                                              .secondary
                                              .withAlpha(51),
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            Theme.of(context)
                                                .colorScheme
                                                .secondary,
                                          ),
                                        ),
                                        const YMargin(8),
                                        Text(
                                          '${(_uploadProgress * 100).toInt()}% Complete',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )
                            : LuxButton(
                                width: 320,
                                disabled: _selectedImages.isEmpty,
                                onTap: handleImageUpload,
                                text: 'Upload Car Images',
                              ),
                      ),
                      const YMargin(10),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImagePreview(Size mediaQuery, bool canPop) {
    return Center(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(bottom: 0.0),
          child: Column(
            children: [
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _selectedImages.map((image) {
                  final isMainImage = mainImageId == image['name'];
                  final hasGoodQuality = _hasGoodImageQuality(image);

                  return GestureDetector(
                    onTap: () {
                      if (!canPop) {
                        setState(() {
                          mainImageId = image['name'];
                        });
                      }
                    },
                    child: Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isMainImage
                                  ? Palette.kFFB800
                                  : hasGoodQuality
                                      ? Colors.green.withValues(alpha: 0.5)
                                      : Colors.orange.withValues(alpha: 0.5),
                              width: isMainImage ? 3 : 1,
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              File(image['uri']),
                              width: mediaQuery.width * 0.28,
                              height: mediaQuery.height * 0.18,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        // Main image indicator
                        if (isMainImage)
                          Positioned(
                            top: 4,
                            right: 4,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Palette.kFFB800,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.star,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        // Quality indicator
                        Positioned(
                          bottom: 4,
                          left: 4,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  hasGoodQuality ? Colors.green : Colors.orange,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              hasGoodQuality ? 'HD' : 'OK',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        // Image info overlay
                        Positioned(
                          bottom: 4,
                          right: 4,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.7),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${image['width'].toInt()}×${image['height'].toInt()}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
              if (!canPop && _selectedImages.isNotEmpty) ...[
                const YMargin(12),
                Text(
                  'Tap an image to set as main photo',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Image.asset(kImgSilhouetteIcon, fit: BoxFit.cover),
        ),
        if (_isLoading) ...[
          Positioned.fill(
            child: Container(color: Colors.black.withValues(alpha: 0.6)),
          ),
          const Positioned.fill(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(color: Colors.amber),
                ),
                YMargin(8),
                Text(
                  'Processing images...',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Enhanced image quality validation
  bool _hasGoodImageQuality(Map<String, dynamic> image) {
    final width = image['width'] as double;
    final height = image['height'] as double;

    return width >= recommendedMinWidth && height >= recommendedMinHeight;
  }

  /// Enhanced image picker with quality validation and user feedback
  Future<void> pickGalleryImages() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final List<XFile> images = await picker.pickMultiImage(
        imageQuality: 95, // High quality for professional car photos
        maxWidth: 2400, // Reasonable max width to prevent huge files
        maxHeight: 1600, // Maintain aspect ratio
      );

      if (images.isEmpty) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Limit the number of images to the max allowed count
      List<XFile> selectedImages = images;
      if (images.length > maxImageLength) {
        Toast.error('You can only select up to $maxImageLength images.');
        selectedImages = images.take(maxImageLength).toList();
      }

      List<Map<String, dynamic>> processedImages = [];
      List<String> qualityWarnings = [];
      List<String> rejectedImages = [];

      // Set the first image as main if in create car flow
      if (mounted && !context.canPop() && selectedImages.isNotEmpty) {
        mainImageId = selectedImages.first.path.split('/').last;
      }

      for (var image in selectedImages) {
        final File imageFile = File(image.path);

        // Get the file size in bytes
        final int fileSizeInBytes = await imageFile.length();
        final double fileSizeInMB = fileSizeInBytes / (1024 * 1024);

        // Check if the image exceeds the size limit
        if (fileSizeInMB > maxImageSizeInMB) {
          rejectedImages.add(
            '${image.name} (${fileSizeInMB.toStringAsFixed(1)}MB)',
          );
          continue;
        }

        final dimensions = await _getImageDimensions(imageFile);
        final imageData = {
          'uri': image.path,
          'width': dimensions.width,
          'height': dimensions.height,
          'name': image.path.split('/').last,
          'sizeInMB': fileSizeInMB.toStringAsFixed(2),
        };

        // Quality validation with user feedback
        if (!_hasGoodImageQuality(imageData)) {
          qualityWarnings.add(
            '${image.name} (${dimensions.width.toInt()}×${dimensions.height.toInt()}px)',
          );
        }

        processedImages.add(imageData);
      }

      // Show feedback to user about image quality
      if (rejectedImages.isNotEmpty) {
        Toast.error(
          'Images rejected (exceed ${maxImageSizeInMB.toInt()}MB): ${rejectedImages.join(', ')}',
        );
      }

      if (qualityWarnings.isNotEmpty && qualityWarnings.length <= 3) {
        Toast.info(
          'Consider using higher resolution images for better quality: ${qualityWarnings.join(', ')}',
          seconds: 5,
        );
      } else if (qualityWarnings.isNotEmpty) {
        Toast.info(
          '${qualityWarnings.length} images have lower resolution. Consider using $recommendedMinWidth×${recommendedMinHeight}px+ for best results.',
          seconds: 5,
        );
      }

      if (processedImages.isNotEmpty) {
        setState(() {
          _selectedImages = processedImages;
          _images = selectedImages.where((image) {
            final fileName = image.path.split('/').last;
            return processedImages.any(
              (processed) => processed['name'] == fileName,
            );
          }).toList();
        });

        // Show success message with quality summary
        final highQualityCount =
            processedImages.where(_hasGoodImageQuality).length;
        if (highQualityCount == processedImages.length) {
          Toast.success(
            '${processedImages.length} high-quality images selected!',
          );
        } else {
          Toast.info(
            '${processedImages.length} images selected ($highQualityCount high-quality)',
          );
        }
      }
    } catch (error) {
      Toast.error('Error selecting images: $error');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Enhanced image upload with progress tracking and quality optimization
  Future<void> handleImageUpload() async {
    if (_selectedImages.isEmpty) return;

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    HomeScreenState.drawerKey.currentState?.closeDrawer();

    // Show upload progress with quality summary
    final highQualityCount = _selectedImages.where(_hasGoodImageQuality).length;
    final qualityMessage = highQualityCount == _selectedImages.length
        ? 'Starting upload of ${_selectedImages.length} high-quality images...'
        : 'Starting upload of ${_selectedImages.length} images ($highQualityCount high-quality)...';

    Toast.info(qualityMessage);

    try {
      final dio = createDioInstance(
        baseUrl: config.baseUrl,
        authToken: ref.read(authControllerProvider).authToken,
        connectTimeout: 90, // Increased timeout for high-quality images
        sendTimeout: 300, // Increased for 5MB images
        receiveTimeout: 300, // Increased for processing time
      );

      List<MultipartFile> files = [];

      // Prepare the images for upload with enhanced metadata
      for (var image in _images) {
        final fileName = image.path.split('/').last;

        // Create multipart file with optimized filename for backend processing
        final multipartFile = await MultipartFile.fromFile(
          image.path,
          filename:
              'car_photo_${DateTime.now().millisecondsSinceEpoch}_$fileName',
        );

        files.add(multipartFile);
      }

      final formFields = {
        "images": files,
        "mainImageId": mainImageId,
        'carId': widget.carId,
        // Additional metadata for backend optimization
        'imageCount': _selectedImages.length.toString(),
        'highQualityCount': highQualityCount.toString(),
      };

      // Remove mainImageId if not in create car flow
      if (mounted && context.canPop()) {
        formFields.remove('mainImageId');
      }

      // Create the form data for all images
      FormData formData = FormData.fromMap(formFields);

      final authToken = ref.read(authControllerProvider).authToken;

      final response = await dio.post(
        'cars/images',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'multipart/form-data',
          },
        ),
        onSendProgress: (sent, total) {
          final progress = sent / total;
          setState(() {
            _uploadProgress = progress;
          });
          // Log progress every 10%
          if ((progress * 100).toInt() % 10 == 0) {
            debugPrint('Upload progress: ${(progress * 100).toInt()}%');
          }
        },
      );

      if (response.statusCode == 200) {
        ref
            .read(fleetControllerProvider.notifier)
            .runImageJob(widget.carId)
            .ignore();

        setState(() {
          _isUploading = false;
          _uploadProgress = 0.0;
        });

        // Enhanced success message with processing info
        Toast.success(
          'Images uploaded successfully! Our system is optimizing them for the best quality. '
          'This may take a few moments to reflect.',
          seconds: 8,
        );

        if (mounted) {
          if (context.canPop()) {
            context.pop();
          } else {
            context.goNamed(homePath);
          }
        }
      } else {
        setState(() {
          _isUploading = false;
          _uploadProgress = 0.0;
        });
        Toast.error("Failed to upload images: ${response.statusCode}");
      }
    } on DioException catch (dioError) {
      setState(() {
        _isUploading = false;
        _uploadProgress = 0.0;
      });

      // Enhanced error handling for different scenarios
      if (dioError.type == DioExceptionType.sendTimeout ||
          dioError.type == DioExceptionType.receiveTimeout) {
        Toast.error(
          "Upload timeout. Please check your connection and try again with fewer or smaller images.",
          seconds: 6,
        );
      } else if (dioError.type == DioExceptionType.connectionError) {
        Toast.error(
          "Connection error. Please check your internet connection and try again.",
          seconds: 5,
        );
      } else if (dioError.response?.statusCode == 413) {
        Toast.error(
          "Images too large. Please reduce image sizes and try again.",
          seconds: 5,
        );
      } else {
        Toast.error("Upload failed: ${dioError.message}");
      }
    } catch (error) {
      setState(() {
        _isUploading = false;
        _uploadProgress = 0.0;
      });
      Toast.error("Unexpected error uploading images: $error");
    }
  }

  // Method to get image dimensions
  Future<ui.Image> _loadImage(File file) async {
    final data = await file.readAsBytes();
    final codec = await ui.instantiateImageCodec(data);
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  Future<ui.Size> _getImageDimensions(File file) async {
    final ui.Image image = await _loadImage(file);
    return ui.Size(image.width.toDouble(), image.height.toDouble());
  }
}

// class UploadCarPhotosScreen extends ConsumerStatefulWidget {
//   const UploadCarPhotosScreen(this.carId, {super.key});

//   final String carId;

//   @override
//   ConsumerState<ConsumerStatefulWidget> createState() =>
//       _UploadCarPhotosState();
// }

// class _UploadCarPhotosState extends ConsumerState<UploadCarPhotosScreen> {
//   final picker = ImagePicker();
//   List<Map<String, dynamic>> _selectedImages = [];
//   List<XFile> _images = [];
//   bool _isLoading = false;
//   String mainImageId = '';
//   late int maxImageLength;

//   @override
//   void initState() {
//     int imageLength =
//         ref.read(fleetControllerProvider).carInView?.images.length ?? 0;
//     maxImageLength = maxImageCount - imageLength;
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final mediaQuery = MediaQuery.sizeOf(context);

//     final canPop = context.canPop(); // cannot pop from create car flow

//     return SafeArea(
//       child: PopScope(
//         canPop: canPop,
//         onPopInvokedWithResult: (_, __) {
//           if (!canPop) {
//             Toast.error('You need to upload at least one photo for this car');
//           }
//         },
//         child: Scaffold(
//           body: CustomScrollView(
//             slivers: [
//               if (canPop)
//                 const SliverAppBar(
//                   leading: BackButton(),
//                 ),
//               SliverFillRemaining(
//                 hasScrollBody: false,
//                 child: Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 16),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       const YMargin(18),
//                       Text(
//                         'Upload Car Pictures',
//                         style: textTheme.titleLarge?.copyWith(
//                           fontWeight: FontWeight.w700,
//                         ),
//                       ),
//                       const YMargin(10),
//                       Text(
//                         "Elevate your car listing and entice potential customers by sharing high quality snapshots that highlight the strengths and extras. Upload multiple photos and make your car appeal shine.",
//                         style: textTheme.bodyMedium,
//                       ),
//                       const YMargin(30),
//                       _selectedImages.isNotEmpty
//                           ? Center(
//                               child: SingleChildScrollView(
//                                 child: Padding(
//                                   padding: const EdgeInsets.only(bottom: 0.0),
//                                   child: Column(
//                                     children: [
//                                       Wrap(
//                                         children: _selectedImages.map((image) {
//                                           return GestureDetector(
//                                             onTap: () {
//                                               if (!canPop) {
//                                                 setState(() {
//                                                   mainImageId = image['name'];
//                                                 });
//                                               }
//                                             },
//                                             child: Stack(
//                                               children: [
//                                                 ClipRRect(
//                                                   borderRadius:
//                                                       BorderRadius.circular(5),
//                                                   child: Image.file(
//                                                     File(image['uri']),
//                                                     width:
//                                                         mediaQuery.width * 0.3,
//                                                     height:
//                                                         mediaQuery.height * 0.2,
//                                                     fit: BoxFit.cover,
//                                                   ),
//                                                 ),
//                                                 if (mainImageId ==
//                                                     image['name'])
//                                                   const Positioned(
//                                                     bottom: 5,
//                                                     right: 5,
//                                                     child: Icon(
//                                                       Icons.check_circle,
//                                                       color: Colors
//                                                           .amber, // Your brand color
//                                                       size: 30,
//                                                     ),
//                                                   ),
//                                               ],
//                                             ),
//                                           );
//                                         }).toList(),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               ),
//                             )
//                           : Stack(
//                               children: [
//                                 ClipRRect(
//                                   borderRadius: BorderRadius.circular(15),
//                                   child: Image.asset(
//                                     kImgSilhouetteIcon,
//                                     fit: BoxFit.cover,
//                                   ),
//                                 ),
//                                 if (_isLoading) ...[
//                                   Positioned.fill(
//                                     child: Container(
//                                       color: Colors.black.withValues(alpha:
//                                           0.6), // Transparent overlay
//                                     ),
//                                   ),
//                                   const Positioned.fill(
//                                     child: Column(
//                                       mainAxisAlignment:
//                                           MainAxisAlignment.center,
//                                       children: [
//                                         SizedBox(
//                                           width: 30,
//                                           height: 30,
//                                           child: CircularProgressIndicator(
//                                             color: Colors.amber,
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ],
//                               ],
//                             ),
//                       const YMargin(30),
//                       GestureDetector(
//                         onTap: pickGalleryImages,
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             const Icon(
//                               Icons.cloud_upload,
//                             ),
//                             const XMargin(8),
//                             Text(
//                               'Select Car Pictures',
//                               style: textTheme.bodyLarge?.copyWith(
//                                 color: Palette.kFFB800,
//                                 fontWeight: FontWeight.w600,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       Padding(
//                         padding: const EdgeInsets.symmetric(horizontal: 16)
//                             .copyWith(top: 20),
//                         child: Text(
//                           'Add up to $maxImageLength photos. Each photo must  be 2MB max',
//                           textAlign: TextAlign.center,
//                           style:
//                               Theme.of(context).textTheme.bodyLarge?.copyWith(
//                                     fontWeight: FontWeight.w600,
//                                   ),
//                         ),
//                       ),
//                       const Spacer(),
//                       const YMargin(30),
//                       Center(
//                         child: LuxButton(
//                           width: 320,
//                           disabled: _selectedImages.isEmpty,
//                           onTap: handleImageUpload,
//                           text: 'Upload Car Images',
//                         ),
//                       ),
//                       const YMargin(10),
//                     ],
//                   ),
//                 ),
//               )
//             ],
//           ),
//         ),
//       ),
//     );
//   } // For File operations For File operations

//   Future<void> pickGalleryImages() async {
//     if (_isLoading) return;

//     setState(() {
//       _isLoading = true;
//     });

//     try {
//       final List<XFile> images = await picker.pickMultiImage();

//       if (images.isEmpty) return;

//       // Limit the number of images to the max allowed count
//       if (images.length > maxImageLength) {
//         Toast.error('You can only select up to $maxImageLength images.');
//         images.removeRange(
//             maxImageCount, images.length); // Retain only the first nth images
//       }

//       List<Map<String, dynamic>> processedImages = [];

//       if (mounted && !context.canPop()) {
//         mainImageId = images.first.path
//             .split('/')
//             .last; // Set the first image as the main one
//       }

//       for (var image in images) {
//         final File imageFile = File(image.path);

//         // Get the file size in bytes
//         final int fileSizeInBytes = await imageFile.length();

//         // Convert the file size to MB
//         final double fileSizeInMB = fileSizeInBytes / (1024 * 1024);

//         // Check if the image exceeds the size limit
//         if (fileSizeInMB > maxImageSizeInMB) {
//           Toast.error(
//               'Image ${image.name} exceeds the ${maxImageSizeInMB}MB size limit.');
//           continue; // Skip this image and move to the next
//         }

//         final dimensions = await _getImageDimensions(imageFile);

//         processedImages.add({
//           'uri': image.path,
//           'width': dimensions.width,
//           'height': dimensions.height,
//           'name': image.path.split('/').last,
//           'sizeInMB': fileSizeInMB
//               .toStringAsFixed(2), // Optional: store size information
//         });
//       }

//       setState(() {
//         _selectedImages = processedImages;
//         _images = images;
//       });
//     } catch (error) {
//       Toast.error('Error picking images: $error');
//     } finally {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   Future<void> handleImageUpload() async {
//     if (_selectedImages.isEmpty) return;

//     HomeScreenState.drawerKey.currentState?.closeDrawer();

//     Loader.show(context);

//     try {
//       final dio = createDioInstance(
//         baseUrl: config.baseUrl,
//         authToken: ref.read(authControllerProvider).authToken,
//         connectTimeout: 60,
//         sendTimeout: 180,
//         receiveTimeout: 180,
//       );

//       List<MultipartFile> files = [];

//       // Prepare the images for upload
//       for (var image in _images) {
//         final fileName = image.path.split('/').last;
//         final multipartFile = await MultipartFile.fromFile(image.path,
//             filename: 'photo_$fileName');

//         files.add(multipartFile);
//       }

//       final formFields = {
//         "images": files,
//         "mainImageId": mainImageId,
//         'carId': widget.carId,
//       };

//       if (mounted && context.canPop()) {
//         formFields.remove('mainImageId');
//       }

//       // Create the form data for all images
//       FormData formData = FormData.fromMap(formFields);

//       final authToken = ref.read(authControllerProvider).authToken;

//       final response = await dio.post(
//         'cars/images',
//         data: formData,
//         options: Options(
//           headers: {
//             'Authorization': 'Bearer $authToken',
//             'Content-Type': 'multipart/form-data',
//           },
//         ),
//       );

//       if (response.statusCode == 200) {
//         ref
//             .read(fleetControllerProvider.notifier)
//             .runImageJob(widget.carId)
//             .ignore();
//         Loader.hide();
//         Toast.success('Give it a while for the photos to reflect.', seconds: 7);
//         if (mounted) {
//           if (context.canPop()) {
//             context.pop();
//           } else {
//             context.goNamed(homePath);
//           }
//         }
//       } else {
//         Loader.hide();
//         Toast.error("Failed to upload pictures: ${response.statusCode}");
//       }
//     } catch (error) {
//       Loader.hide();
//       Toast.error("Error uploading images: $error");
//     }
//   }

//   // Method to get image dimensions
//   Future<ui.Image> _loadImage(File file) async {
//     final data = await file.readAsBytes();
//     final codec = await ui.instantiateImageCodec(data);
//     final frame = await codec.getNextFrame();
//     return frame.image;
//   }

//   Future<ui.Size> _getImageDimensions(File file) async {
//     final ui.Image image = await _loadImage(file);
//     return ui.Size(image.width.toDouble(), image.height.toDouble());
//   }
// }
