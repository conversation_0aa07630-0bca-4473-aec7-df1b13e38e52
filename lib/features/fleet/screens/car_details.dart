import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:fleet_mobile/features/fleet/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class CarDetailsScreen extends ConsumerWidget {
  const CarDetailsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    final car = ref.watch(fleetControllerProvider).carInView!;

    return SafeArea(
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            const SliverAppBar(
              leading: BackButton(),
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const YMargin(10),
                    Text(
                      car.name.toUpperCase(),
                      style: textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const YMargin(10),
                    RatingWidget(rating: car.ratingSummary),
                    const YMargin(20),
                    if (car.imageUrl == null)
                      Container(
                        height: 200,
                        width: double.maxFinite,
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withValues(alpha: 77)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                      )
                    else
                      Hero(
                        tag: 'car_list_${car.id}',
                        child: Container(
                          height: 200,
                          width: double.maxFinite,
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withValues(alpha: 77)),
                            borderRadius: BorderRadius.circular(10),
                            image: DecorationImage(
                              fit: BoxFit.cover,
                              image: NetworkImage(
                                car.imageUrl!, // Replace with your image URL
                              ),
                            ),
                          ),
                        ),
                      ),
                    const YMargin(40),
                    _buildDetailsSection(context, car),
                    const YMargin(40),
                    _buildActionButtons(context, car, ref),
                    const YMargin(40),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection(BuildContext context, Car car) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(10),
          border:
              Border.all(color: colorScheme.secondary.withValues(alpha: 77)),
        ),
        child: Column(
          children: [
            _buildDetailRow(context, 'Daily Price:', car.dailyPrice, true),
            Divider(color: colorScheme.onSurface.withValues(alpha: 77)),
            _buildDetailRow(context, 'Hourly Price:', car.hourlyPrice, true),
            Divider(color: colorScheme.onSurface.withValues(alpha: 77)),
            _buildDetailRow(context, 'Bookings:', car.bookingCount),
            Divider(color: colorScheme.onSurface.withValues(alpha: 77)),
            _buildDetailRow(context, 'Earnings:', car.totalEarned, true),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, dynamic value,
      [bool isCurrency = false]) {
    final textTheme = Theme.of(context).textTheme;
    return Row(
      children: [
        Expanded(
          child: Align(
            alignment: Alignment.centerRight,
            child: Text(
              label,
              style: textTheme.bodyLarge,
            ),
          ),
        ),
        const XMargin(10),
        Container(
          height: 20,
          width: 1,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 77),
        ),
        const XMargin(10),
        Expanded(
          child: Align(
            alignment: Alignment.centerLeft,
            child: isCurrency
                ? Text.rich(
                    TextSpan(
                      text: '',
                      children: [
                        TextSpan(
                          text: CurrencyItem.currencySymbol(context, 'NGN'),
                          style: textTheme.bodyLarge?.copyWith(
                            fontFamily: 'roboto',
                            color: Theme.of(context).colorScheme.tertiary,
                          ),
                        ),
                        TextSpan(
                          text: CurrencyItem.formatAmount(context, value),
                          style: textTheme.bodyLarge?.copyWith(
                            color: Theme.of(context).colorScheme.tertiary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  )
                : Text(
                    '$value',
                    style: textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.tertiary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, Car car, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildActionButton(
            context,
            'View Images',
            Colors.amber,
            () {
              context.pushNamed(carImagesPath);
            },
          ),
          const YMargin(10),
          _buildActionButton(
            context,
            'Set Price/Location',
            Colors.orange,
            () {
              context.pushNamed(updateCarPath);
            },
          ),
          const YMargin(10),
          _buildActionButton(
            context,
            'Delist Car',
            Colors.red,
            () => onCarDelist(context, car, ref),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
      BuildContext context, String label, Color color, VoidCallback onTap) {
    final textTheme = Theme.of(context).textTheme;
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          foregroundColor: color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Text(
          label,
          style: textTheme.bodyLarge?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ),
    );
  }

  Future<void> onCarDelist(BuildContext context, Car car, WidgetRef ref) async {
    showConfirmationDialog(
      context,
      'Delist Car',
      'Are you sure you want to delist this car?',
      'Delist',
      actionTextColor: Colors.red,
      onConfirm: () async {
        Navigator.of(context).pop();

        Loader.show(context);

        final res =
            await ref.read(fleetControllerProvider.notifier).delistCar(car.id);

        res.when(
          (_) {
            Loader.hide();
            context.pop();
            Toast.success('The car was delisted successfully.');
          },
          (error) {
            Loader.hide();
            Toast.error(error.message);
          },
        );
        // ScaffoldMessenger.of(context).showSnackBar(
        //   const SnackBar(content: Text('Successfully  delist car')),
        // );
      },
    );
  }
}
