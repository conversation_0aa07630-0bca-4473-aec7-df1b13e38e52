import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:fleet_mobile/features/fleet/widgets/widgets.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CarListingScreen extends ConsumerWidget {
  const CarListingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final cars = ref.watch(fleetControllerProvider).cars;

    return SafeArea(
      child: Scaffold(
        body: Stack(
          children: [
            RefreshIndicator(
              color: Palette.kFFB800,
              onRefresh: () async {
                await ref.read(fleetControllerProvider.notifier).loadCars();
              },
              child: CustomScrollView(
                slivers: [
                  const SliverAppBar(
                    leading: BackButton(),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 18.0),
                      child: Text(
                        'All Cars',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  cars.when(
                    data: (cars) {
                      if (cars.isEmpty) {
                        return SliverFillRemaining(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Stack(
                                alignment: Alignment.center,
                                children: [
                                  Icon(
                                    Icons.directions_car_outlined,
                                    size: 80,
                                    color: Theme.of(context)
                                        .primaryColorLight
                                        .withValues(alpha: 0.5),
                                  ),
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: const BoxDecoration(
                                        color: Palette.k040302,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.add,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const YMargin(24),
                              Text(
                                'No Cars Yet',
                                style: textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const YMargin(8),
                              Text(
                                'Start by uploading your first car\nto your fleet',
                                style: textTheme.bodyLarge?.copyWith(
                                  color: Colors.grey[600],
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const YMargin(56),
                            ],
                          ),
                        );
                      }

                      return SliverList.builder(
                        itemCount: cars.length,
                        itemBuilder: (context, index) {
                          return CarCard(cars[index]);
                        },
                      );
                    },
                    loading: () {
                      return SliverSkeletonizer(
                        enabled: true,
                        child: SliverList.builder(
                          itemCount: 1,
                          itemBuilder: (context, index) {
                            return CarCard(Car.defaultValue());
                          },
                        ),
                      );
                    },
                    error: (e, s) {
                      return SliverToBoxAdapter(
                        child: FailureWidget(
                          fullScreen: true,
                          e: e,
                          retry: () async {
                            await ref
                                .read(fleetControllerProvider.notifier)
                                .loadCars();
                          },
                        ),
                      );
                    },
                  ),
                  if ((cars.asData?.value.length ?? 0) > 3)
                    const SliverPadding(padding: EdgeInsets.only(bottom: 80))
                ],
              ),
            ),
            Positioned(
              bottom: 0,
              right: 50,
              left: 50,
              child: LuxButton(
                onTap: () => context.pushNamed(addCarPath),
                text: 'Upload Car',
                icon: const Icon(Icons.add_circle_outline),
                width: 340,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
