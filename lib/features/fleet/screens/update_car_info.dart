import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/widgets/widgets.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:fleet_mobile/features/fleet/fleet_params.dart';
import 'package:fleet_mobile/features/fleet/fleet_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class UpdateCarInfoScreen extends ConsumerStatefulWidget {
  const UpdateCarInfoScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _UpdateCarInfoScreenState();
}

class _UpdateCarInfoScreenState extends ConsumerState<UpdateCarInfoScreen> {
  @override
  void initState() {
    car = ref.read(fleetControllerProvider).carInView!;

    controllers['address']?.text = car.address?.address ?? '';
    controllers['dailyMinPrice']?.text = car.dailyMinPrice.toString();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return FormScreen(
      formKey: formKey,
      showLogoImage: true,
      title: 'Update Car Info',
      submitButtonText: 'Update',
      handleSubmit: () => handleSubmit(context, ref),
      body: [
        LuxAddressField(
          isRequired: true,
          // filled: true,
          title: 'Car Location',
          hint: 'Search for car location',
          controller: controllers['address'],
          onLocationSelected: (location) {
            address = location;
          },
        ),
        LuxTextField(
          title: 'Daily Min Price',
          hint: 'Enter daily min price',
          textFieldType: TextFieldType.text,
          keyboardType: TextInputType.number,
          textFieldController: controllers['dailyMinPrice'],
          isRequired: true,
          // filled: true,
          onSubmitted: () {
            FocusScope.of(context).nextFocus();
          },
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            CurrencyInputFormatter(),
          ],
        ),
        const YMargin(20),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAvailabilityRow(
                'Is Available (Day)',
                daySchedule,
                textTheme,
                (value) {
                  setState(() {
                    // If day is set to false, night must be true
                    daySchedule = value;
                    if (!value) {
                      nightSchedule = true;
                    }
                  });
                },
              ),
              const YMargin(20),
              _buildAvailabilityRow(
                'Is Available (Night)',
                nightSchedule,
                textTheme,
                (value) {
                  setState(() {
                    // If night is set to false, day must be true
                    nightSchedule = value;
                    if (!value) {
                      daySchedule = true;
                    }
                  });
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  final formKey = GlobalKey<FormState>();

  late Car car;

  late final day =
      car.schedule == Schedule.dayNight || car.schedule == Schedule.dayOnly;
  late final night =
      car.schedule == Schedule.dayNight || car.schedule == Schedule.nightOnly;

  LuxAddress? address;
  late bool daySchedule = day;
  late bool nightSchedule = night;

  final Map<String, TextEditingController> controllers = {
    'address': TextEditingController(),
    'dailyMinPrice': TextEditingController(),
  };

  Future<void> handleSubmit(BuildContext context, WidgetRef ref) async {
    final data = controllers.data();

    if (address != null) {
      data['address'] = address?.toMap();
    } else {
      data.remove('address');
    }

    if (data['dailyMinPrice'] != null &&
        data['dailyMinPrice'].toString().isNotEmpty) {
      data['dailyMinPrice'] =
          num.parse(data['dailyMinPrice'].toString().replaceAll(',', ''));
    }

    data['schedule'] = processSchedule(daySchedule, nightSchedule);

    final params = UpdateCarParams(data: data, carId: car.id);

    Loader.show(context);

    final res = await ref.read(fleetServiceProvider).updateCar(params);
    res.when(
      (_) {
        Loader.hide();
        Toast.success('Car was updated successfully');
        final fleetController = ref.read(fleetControllerProvider.notifier);
        fleetController.updateCarInfo(
          carId: car.id,
          address: address,
          dailyMinPrice: data['dailyMinPrice'],
          schedule: Schedule.fromString(data['schedule']),
        );
        fleetController.updateCarInView(car.copyWith(
            address: address,
            dailyMinPrice: data['dailyMinPrice'],
            schedule: Schedule.fromString(data['schedule'])));
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }

  String processSchedule(bool day, bool night) {
    if (day && night) {
      return 'Day/Night';
    } else if (day) {
      return 'Day Only';
    } else if (night) {
      return 'Night Only';
    } else {
      return '';
    }
  }

  Widget _buildAvailabilityRow(
    String label,
    bool switchValue,
    TextTheme textTheme,
    ValueChanged<bool> onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: textTheme.bodyLarge?.copyWith(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const YMargin(5),
              Row(
                children: [
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        switchValue ? 'Available' : 'Unavailable',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  ),
                  Container(
                    height: 20,
                    width: 1,
                    color: Colors.grey,
                  ),
                  const XMargin(10),
                  Switch(
                    value: switchValue,
                    onChanged: onChanged,
                    activeColor: Colors.green,
                    inactiveThumbColor: Colors.white,
                    inactiveTrackColor: Colors.grey,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
