import 'package:cached_network_image/cached_network_image.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:fleet_mobile/features/fleet/fleet_params.dart';
import 'package:fleet_mobile/features/fleet/fleet_service.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CarImagesScreen extends ConsumerStatefulWidget {
  const CarImagesScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CarImagesScreenState();
}

class _CarImagesScreenState extends ConsumerState<CarImagesScreen> {
  late Car car;
  late List<CarImage> images = car.images;
  late int maxImageLength;

  CarImage? get mainImage => images.isEmpty
      ? null
      : images.firstWhere((img) => img.isMain, orElse: () => images.first);
  // List<CarImage> get otherImages =>
  //     images.where((img) => (img.id != mainImage.id)).toList();

  @override
  void initState() {
    car = ref.read(fleetControllerProvider).carInView!;

    int imageLength =
        ref.read(fleetControllerProvider).carInView?.images.length ?? 0;
    maxImageLength = maxImageCount - imageLength;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(fleetControllerProvider, (previousState, nextState) {
      final prevCarInView = previousState?.carInView;
      final nextCarInView = nextState.carInView;

      // Previous and next images list lengths
      final prevImagesLength = prevCarInView?.images.length ?? 0;
      final nextImagesLength = nextCarInView?.images.length ?? 0;

      // Check if there are new images (new images uploaded)
      if (nextImagesLength > prevImagesLength) {
        setState(() {
          car = nextCarInView!;
          images = nextCarInView.images;
        });
        Toast.success('Your updated photo(s) are now visible');
      }
      // Check if the main image or visibility has changed
      else if (!_areImagesEqual(prevCarInView?.images, nextCarInView?.images)) {
        setState(() {
          car = nextCarInView!;
          images = nextCarInView.images;
        });
      }
    });

    final mainImageIndex = images.isNotEmpty ? images.indexOf(mainImage!) : 0;

    return SafeArea(
      child: Scaffold(
        body: Stack(
          children: [
            CustomScrollView(
              slivers: [
                SliverAppBar(
                  leading: images.isEmpty
                      ? const BackButton(
                          color: Colors.white,
                        )
                      : const CircleAvatar(
                          backgroundColor: Colors.white,
                          child: BackButton(
                            color: Colors.black,
                          ),
                        ),
                  expandedHeight: 300.0,
                  pinned: true,
                  flexibleSpace: images.isNotEmpty
                      ? FlexibleSpaceBar(
                          background: GestureDetector(
                            onTap: () {
                              _showImageDialog(
                                  context, mainImage!, mainImageIndex);
                            },
                            child: Hero(
                              tag: mainImage!.id,
                              child: LuxNetworkImage(
                                mainImage?.url,
                                fit: BoxFit.cover,
                                // width: double.infinity,
                                // height: double.infinity,
                              ),
                            ),
                          ),
                          title: const Text(
                            'Car Images',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
                images.isEmpty
                    ? SliverFillRemaining(
                        child: Column(
                          children: [
                            Text(
                              'No images available. \nPlease upload images below',
                              style: Theme.of(context).textTheme.bodyLarge,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                    : SliverPadding(
                        padding:
                            const EdgeInsets.all(16.0).copyWith(bottom: 75),
                        sliver: SliverGrid(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 10,
                            mainAxisSpacing: 10,
                            childAspectRatio: 1.0,
                          ),
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final image = images[index];
                              return Builder(builder: (context) {
                                final loadingValue = ValueNotifier<bool>(false);
                                return ValueListenableBuilder<bool>(
                                    valueListenable: loadingValue,
                                    builder: (context, loading, _) {
                                      return GestureDetector(
                                        onTap: () => _showImageDialog(
                                            context, image, index),
                                        child: Skeletonizer(
                                          enabled: loading,
                                          child: Stack(
                                            fit: StackFit.expand,
                                            children: [
                                              // Image Background
                                              Container(
                                                decoration: BoxDecoration(
                                                  image: DecorationImage(
                                                    image:
                                                        CachedNetworkImageProvider(
                                                            image.url),
                                                    fit: BoxFit.cover,
                                                    colorFilter: image.isVisible
                                                        ? null
                                                        : ColorFilter.mode(
                                                            Colors.black
                                                                .withValues(
                                                                    alpha: 0.5),
                                                            BlendMode.darken),
                                                  ),
                                                ),
                                                child: image.isVisible
                                                    ? null
                                                    : Container(
                                                        color: Colors.black
                                                            .withValues(
                                                                alpha: 0.1),
                                                        child: const Center(
                                                          child: Text(
                                                            'Hidden from clients',
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                            ),
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                          ),
                                                        ),
                                                      ),
                                              ),
                                              // Icon Button for visibility toggle
                                              Positioned(
                                                bottom: 5,
                                                right: 5,
                                                child: Skeleton.replace(
                                                  child: CircleAvatar(
                                                    child: IconButton(
                                                      icon: image.isVisible
                                                          ? const Icon(
                                                              Icons.visibility)
                                                          : const Icon(
                                                              Icons
                                                                  .visibility_off,
                                                              color:
                                                                  Colors.white),
                                                      onPressed: () {
                                                        _changeVisibility(
                                                            image);
                                                      },
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              // Icon Button to delete an image
                                              Positioned(
                                                top: 1,
                                                left: 1,
                                                child: Skeleton.replace(
                                                  child: Container(
                                                    color: Colors.black,
                                                    child: InkWell(
                                                      child: const Icon(
                                                          Icons.delete,
                                                          color: Colors.red),
                                                      onTap: () {
                                                        showConfirmationDialog(
                                                            context,
                                                            'Delete Image',
                                                            '',
                                                            'Delete',
                                                            actionTextColor:
                                                                Colors.red,
                                                            onConfirm: () {
                                                          Navigator.pop(
                                                              context);
                                                          _deleteImage(image,
                                                              (value) {
                                                            loadingValue.value =
                                                                value;
                                                          });
                                                        });
                                                      },
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    });
                              });
                            },
                            childCount: images.length,
                          ),
                        ),
                      ),
              ],
            ),
            Visibility(
              visible: maxImageLength > 0,
              child: Positioned(
                bottom: 0,
                right: 50,
                left: 50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0).copyWith(bottom: 0),
                  child: SizedBox(
                    height: 50,
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Palette.kFFB800,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onPressed: () {
                        context.pushNamed(uploadCarImagesPath, extra: car.id);
                      },
                      icon: const Icon(Icons.upload),
                      label: Text(images.isEmpty
                          ? 'Upload Images'
                          : 'Upload More Images'),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Function to show an image in a full screen dialog
  void _showImageDialog(
      BuildContext context, CarImage initialImage, int currentIndex) {
    final imageNotifier = ValueNotifier<CarImage>(initialImage);
    final loadingNotifier = ValueNotifier<bool>(false);

    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: EdgeInsets.zero,
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.6,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      PhotoViewGallery.builder(
                        scrollPhysics: const BouncingScrollPhysics(),
                        builder: (BuildContext context, int index) {
                          final image = images[index];
                          return PhotoViewGalleryPageOptions(
                            imageProvider: NetworkImage(image.url),
                            minScale: PhotoViewComputedScale.contained,
                            maxScale: PhotoViewComputedScale.covered * 2,
                          );
                        },
                        itemCount: images.length,
                        onPageChanged: (index) {
                          imageNotifier.value = images[index];
                        },
                        backgroundDecoration: BoxDecoration(
                          color: Theme.of(context)
                              .badgeTheme
                              .backgroundColor, // Dark background
                        ),
                        pageController:
                            PageController(initialPage: currentIndex),
                      ),
                      MultiValueListenableBuilder<bool, CarImage>(
                        valueListenable1: loadingNotifier,
                        valueListenable2: imageNotifier,
                        builder: (context, loading, image, _) {
                          return Positioned(
                            top: loading ? 10 : 1,
                            right: loading ? 25 : 0,
                            child: image.isMain
                                ? const SizedBox.shrink()
                                : loading
                                    ? const CircleAvatar(
                                        child: CupertinoActivityIndicator(
                                          radius: 20,
                                        ),
                                      )
                                    : TextButton(
                                        clipBehavior: Clip.antiAlias,
                                        style: TextButton.styleFrom(
                                          backgroundColor: Palette.k774900,
                                        ),
                                        onPressed: () => _setMainImage(image,
                                            (loading, success) {
                                          // Update loading state
                                          loadingNotifier.value = loading;
                                          if (success) {
                                            // If set as main is successful, update imageNotifier
                                            imageNotifier.value =
                                                image.copyWith(isMain: true);
                                          }
                                        }),
                                        child: Text(
                                          'Set as main image',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyLarge,
                                        ),
                                      ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

// Helper function to check if the visibility or isMain of images has changed
  bool _areImagesEqual(List<CarImage>? prevImages, List<CarImage>? nextImages) {
    if (prevImages == null ||
        nextImages == null ||
        prevImages.length != nextImages.length) {
      return false;
    }

    for (int i = 0; i < prevImages.length; i++) {
      if (prevImages[i].isVisible != nextImages[i].isVisible ||
          prevImages[i].isMain != nextImages[i].isMain) {
        // Check for both isVisible and isMain changes
        return false;
      }
    }

    return true;
  }

  Future<void> _changeVisibility(CarImage image) async {
    // Loader.show(context);

    try {
      // Optimistically update UI before sending the request
      setState(() {
        images = images.map((x) {
          if (x.id == image.id) {
            // Toggle visibility of the selected image
            return x.copyWith(isVisible: !x.isVisible);
          }
          return x;
        }).toList();
      });

      final res = await ref.read(fleetServiceProvider).updateImageVisibility(
          ImageVisibilityParams(
              imageId: image.id, isVisible: !image.isVisible));

      res.when(
        (_) {
          // Update state only through the provider to reflect the change globally
          final fleetController = ref.read(fleetControllerProvider.notifier);

          fleetController.updateImageVisibility(
              car.id, image.id, !image.isVisible);
          fleetController.updateCarInView(car.copyWith(images: images));

          // Notify the user of successful update
          // Loader.hide();
          Toast.success('Image visibility updated successfully');
        },
        (error) {
          // Handle error by reverting the optimistic UI update
          setState(() {
            images = images.map((x) {
              if (x.id == image.id) {
                // Revert visibility if the update fails
                return x.copyWith(isVisible: image.isVisible);
              }
              return x;
            }).toList();
          });
          // Loader.hide();
          Toast.error(error.message);
        },
      );
    } catch (e) {
      // Loader.hide();
      Toast.error("An error occurred while updating image visibility.");
    }
  }

  Future<void> _deleteImage(CarImage image, Function(bool) callback) async {
    callback(true);

    final res =
        await ref.read(fleetServiceProvider).deleteImage(car.id, image.id);

    res.when(
      (_) {
        callback(false);
        ref
            .read(fleetControllerProvider.notifier)
            .deleteCarImage(car.id, image.id);
        // Toast.success('Image was deleted successfully');
      },
      (error) {
        callback(false);
        Toast.error(error.message);
      },
    );
  }

  Future<void> _setMainImage(
      CarImage image, Function(bool, bool) callback) async {
    callback(true, false);

    final res =
        await ref.read(fleetServiceProvider).changeMainImage(car.id, image.id);

    res.when(
      (_) {
        ref
            .read(fleetControllerProvider.notifier)
            .setMainImage(car.id, image.id);
        callback(false, true);
        Toast.success('Image was set as main image successfully');
      },
      (error) {
        callback(false, false);
        Toast.error(error.message);
      },
    );
  }
}
