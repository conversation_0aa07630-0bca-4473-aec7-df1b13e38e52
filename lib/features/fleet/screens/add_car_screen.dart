import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:fleet_mobile/features/fleet/fleet_service.dart';
import 'package:fleet_mobile/features/fleet/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:wc_form_validators/wc_form_validators.dart';

class AddCarScreen extends ConsumerStatefulWidget {
  const AddCarScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AddCarScreenState();
}

class _AddCarScreenState extends ConsumerState<AddCarScreen> {
  @override
  void initState() {
    super.initState();

    Future.microtask(() {
      ref
          .read(fleetControllerProvider.notifier)
          .getBrandsAndCategories()
          .ignore();
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final size = MediaQuery.of(context).size;

    final fleetState = ref.watch(fleetControllerProvider);

    return SafeArea(
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            SliverAppBar(
              leading: const BackButton(),
              title: Text(
                'Add New Car',
                style: textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
              pinned: true,
              backgroundColor: colorScheme.surface,
              elevation: 0,
            ),
            SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.05),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  const YMargin(20),
                  // Progress indicator
                  _buildProgressIndicator(context, 1, 2),
                  const YMargin(30),
                  // Form card
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color:
                            colorScheme.secondary.withAlpha(51), // 0.2 opacity
                      ),
                      boxShadow: [
                        BoxShadow(
                          color:
                              colorScheme.shadow.withAlpha(26), // 0.1 opacity
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Vehicle Information',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.secondary,
                            ),
                          ),
                          const YMargin(20),
                          fleetState.brands.when(
                            data: (brands) {
                              return SelectWidget<Brand>(
                                filter: true,
                                isRequired: true,
                                options: brands,
                                title: 'Car Brand',
                                // filled: true,
                                displayString: (brand) => brand.name,
                                controller: controllers['brandId'],
                                onSelect: (brand) {
                                  controllers['brandId']?.text = brand.name;
                                  selectedBrand.value = brand;
                                },
                              );
                            },
                            loading: () {
                              return Skeletonizer(
                                enabled: true,
                                child: SelectWidget<Brand>(
                                  // filter: true,
                                  isRequired: true,
                                  options: const [],
                                  title: 'Car Brand',
                                  // filled: true,
                                  displayString: (brand) => brand.name,
                                  controller: controllers['brandId'],
                                  onSelect: (selectedBrand) {
                                    controllers['brandId']?.text =
                                        selectedBrand.id;
                                  },
                                ),
                              );
                            },
                            error: (e, s) {
                              return LuxTextField(
                                readonly: true,
                                title: 'Car Brand',
                                hint: '',
                                textFieldType: TextFieldType.text,
                                keyboardType: TextInputType.text,
                                // filled: true,
                                onTap: () {},
                                suffixIcon: TextButton(
                                  child: Text(
                                    'Retry',
                                    style: TextStyle(
                                        color: Theme.of(context).hintColor),
                                  ),
                                  onPressed: () {
                                    ref
                                        .read(fleetControllerProvider.notifier)
                                        .getBrandsAndCategories();
                                  },
                                ),
                                errorText: 'Error fetching brands',
                              );
                            },
                          ),
                          LuxTextField(
                            title: 'Car Model',
                            hint: 'e.g Camry',
                            textFieldType: TextFieldType.text,
                            textFieldController: controllers['model'],
                            isRequired: true,
                            // filled: true,
                            onSubmitted: () {
                              FocusScope.of(context).nextFocus();
                            },
                          ),
                          LuxTextField(
                            title: 'Model Year',
                            hint: 'Enter model year',
                            textFieldType: TextFieldType.text,
                            keyboardType: TextInputType.datetime,
                            textFieldController: controllers['year'],
                            isRequired: true,
                            // filled: true,
                            onSubmitted: () {
                              FocusScope.of(context).nextFocus();
                            },
                            inputFormatters: [
                              LengthLimitingTextInputFormatter(4),
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            validator: [
                              Validators.minLength(4, 'Enter a valid year'),
                              Validators.maxLength(4, 'Enter a valid year'),
                            ],
                          ),
                          fleetState.categories.when(
                            data: (categories) {
                              return SelectWidget<Category>(
                                // filter: true,
                                isRequired: true,
                                options: categories,
                                title: 'Car Category',
                                // filled: true,
                                displayString: (category) => category.name,
                                controller: controllers['categoryId'],
                                onSelect: (category) {
                                  controllers['categoryId']?.text =
                                      category.name;
                                  selectedCategory.value = category;
                                },
                              );
                            },
                            loading: () {
                              return Skeletonizer(
                                enabled: true,
                                child: SelectWidget<Category>(
                                  // filter: true,
                                  isRequired: true,
                                  options: const [],
                                  title: 'Car Category',
                                  // filled: true,
                                  displayString: (category) => category.name,
                                  controller: controllers['categoryId'],
                                  onSelect: (selectedCategory) {
                                    controllers['categoryId']?.text =
                                        selectedCategory.id;
                                  },
                                ),
                              );
                            },
                            error: (e, s) {
                              return LuxTextField(
                                readonly: true,
                                title: 'Car Category',
                                hint: '',
                                textFieldType: TextFieldType.text,
                                keyboardType: TextInputType.text,
                                // filled: true,
                                onTap: () {},
                                suffixIcon: TextButton(
                                  child: Text(
                                    'Retry',
                                    style: TextStyle(
                                        color: Theme.of(context).hintColor),
                                  ),
                                  onPressed: () {
                                    ref
                                        .read(fleetControllerProvider.notifier)
                                        .getBrandsAndCategories();
                                  },
                                ),
                                errorText: 'Error fetching categories',
                              );
                            },
                          ),
                          LuxAddressField(
                            isRequired: true,
                            // filled: true,
                            title: 'Car Location',
                            hint: 'Search for car location',
                            controller: controllers['address'],
                            onLocationSelected: (address) {
                              selectedAddress.value = address;
                            },
                          ),

                          const YMargin(30),
                          // Pricing section
                          Text(
                            'Pricing Information',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.secondary,
                            ),
                          ),
                          const YMargin(20),
                          Row(
                            children: [
                              Expanded(
                                child: LuxTextField(
                                  title: 'Daily Price',
                                  hint: 'Standard daily price',
                                  textFieldType: TextFieldType.text,
                                  keyboardType: TextInputType.number,
                                  textFieldController:
                                      controllers['dailyPrice'],
                                  isRequired: true,
                                  onSubmitted: () {
                                    FocusScope.of(context).nextFocus();
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    CurrencyInputFormatter(),
                                  ],
                                ),
                              ),
                              const XMargin(16),
                              Expanded(
                                child: LuxTextField(
                                  title: 'Daily Min Price',
                                  hint: 'Enter daily min price',
                                  textFieldType: TextFieldType.text,
                                  keyboardType: TextInputType.number,
                                  textFieldController:
                                      controllers['dailyMinPrice'],
                                  isRequired: true,
                                  onSubmitted: () {
                                    FocusScope.of(context).nextFocus();
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    CurrencyInputFormatter(),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SelectWidget<String>(
                            isRequired: true,
                            options: const [
                              'Day/Night',
                              'Day Only',
                              'Night Only'
                            ],
                            title: 'Schedule',
                            displayString: (availability) => availability,
                            controller: controllers['schedule'],
                            onSelect: (schedule) {
                              controllers['schedule']?.text = schedule;
                            },
                          ),
                          const YMargin(30),
                          SizedBox(
                            width: double.infinity,
                            child: LuxButton(
                              onTap: () => handleSubmit(context, ref),
                              text: 'Continue to Photos',
                              icon: const Icon(Icons.arrow_forward),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const YMargin(40),
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  final formKey = GlobalKey<FormState>();

  final selectedAddress = ValueNotifier<LuxAddress?>(null);
  final selectedBrand = ValueNotifier<Brand?>(null);
  final selectedCategory = ValueNotifier<Category?>(null);

  final Map<String, TextEditingController> controllers = {
    'brandId': TextEditingController(),
    'model': TextEditingController(),
    'categoryId': TextEditingController(),
    'year': TextEditingController(),
    'address': TextEditingController(),
    'dailyPrice': TextEditingController(),
    'dailyMinPrice': TextEditingController(),
    'schedule': TextEditingController(),
  };

  Future<void> handleSubmit(BuildContext context, WidgetRef ref) async {
    if (!formKey.currentState!.validate()) return;

    final data = controllers.data();
    data['brandId'] = selectedBrand.value?.id;
    data['categoryId'] = selectedCategory.value?.id;
    data['address'] = selectedAddress.value?.toMap();
    data['dailyPrice'] =
        num.parse(data['dailyPrice'].toString().replaceAll(',', ''));
    data['dailyMinPrice'] =
        num.parse(data['dailyMinPrice'].toString().replaceAll(',', ''));

    Loader.show(context);

    final res = await ref.read(fleetServiceProvider).createCar(data);

    res.when(
      (car) {
        ref.read(fleetControllerProvider.notifier).addCar(car);
        context.goNamed(uploadCarImagesPath, extra: car.id);
        Loader.hide();
      },
      (error) {
        Loader.hide();
        Toast.error(error.message);
      },
    );
  }

  Widget _buildProgressIndicator(
      BuildContext context, int currentStep, int totalSteps) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Step $currentStep of $totalSteps',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Text(
              '${((currentStep / totalSteps) * 100).round()}%',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.secondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const YMargin(8),
        LinearProgressIndicator(
          value: currentStep / totalSteps,
          backgroundColor: colorScheme.secondary.withAlpha(51), // 0.2 opacity
          valueColor: AlwaysStoppedAnimation<Color>(colorScheme.secondary),
        ),
      ],
    );
  }
}
