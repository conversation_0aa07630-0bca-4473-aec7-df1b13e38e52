import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/fleet/fleet_controller.dart';
import 'package:fleet_mobile/features/fleet/widgets/widgets.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => HomeScreenState();
}

class HomeScreenState extends ConsumerState<HomeScreen> {
  static final drawerKey = GlobalKey<ScaffoldState>();
  bool _hasPlayedSound = false;

  @override
  void initState() {
    super.initState();
    _playHornSound();
  }

  Future<void> _playHornSound() async {
    if (_hasPlayedSound) return;

    try {
      final player = ref.read(audioPlayerProvider);
      final playHornSound = ref.read(playHornSoundProvider);
      playHornSound(player);
      _hasPlayedSound = true;
    } catch (e) {
      debugPrint('Error playing horn sound: $e');
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final size = MediaQuery.of(context).size;
    final user = ref.watch(authControllerProvider).user!;
    final cars = ref.watch(fleetControllerProvider).cars;

    return SafeArea(
      child: Scaffold(
        key: drawerKey,
        body: Stack(
          children: [
            RefreshIndicator(
              color: Palette.kFFB800,
              onRefresh: () async {
                await ref.read(fleetControllerProvider.notifier).loadCars();
              },
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.05),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const YMargin(20),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const AppBarMenu(),
                              const XMargin(20),
                              Expanded(
                                child: Text(
                                  'Welcome ${user.firstName}',
                                  style: textTheme.headlineSmall?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    letterSpacing: 0.5,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const YMargin(20),
                          Container(
                            width: size.width * 0.9,
                            padding: const EdgeInsets.symmetric(
                              vertical: 24.0,
                              horizontal: 24.0,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.circular(24),
                              border: Border.all(
                                color: Palette.kFFEABC,
                                width: 2,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Palette.kFFEABC.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                  offset: const Offset(0, 0),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      'Current Balance',
                                      style: textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Palette.kFFFFFF
                                            .withValues(alpha: 0.8),
                                      ),
                                    ),
                                    const YMargin(4),
                                    CurrencyItem(
                                      user.wallet?.availableBalance ?? 0,
                                      style: textTheme.headlineSmall?.copyWith(
                                        fontWeight: FontWeight.w700,
                                        color: Palette.kFFB800,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  cars.when(
                    data: (cars) {
                      return SliverPadding(
                        padding: EdgeInsets.symmetric(
                          horizontal: size.width * 0.05,
                        ),
                        sliver: SliverList(
                          delegate: SliverChildListDelegate([
                            const YMargin(20),
                            // Dashboard Statistics
                            _buildDashboardStats(
                                context, cars, textTheme, colorScheme, ref),
                            const YMargin(24),
                            // Quick Actions
                            _buildQuickActions(context, size),
                            const YMargin(24),
                            // Recent Cars Section
                            if (cars.isNotEmpty) ...[
                              Text(
                                'Your Fleet',
                                style: textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const YMargin(16),
                              ...cars.take(3).map((car) => Padding(
                                    padding: const EdgeInsets.only(bottom: 12),
                                    child: CarCard(car, home: true),
                                  )),
                              if (cars.length > 3) ...[
                                const YMargin(16),
                                Center(
                                  child: TextButton(
                                    onPressed: () =>
                                        context.pushNamed(carListingPath),
                                    child: Text(
                                      'View All Cars (${cars.length})',
                                      style: textTheme.bodyLarge?.copyWith(
                                        color: colorScheme.secondary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ] else ...[
                              _buildEmptyState(context, textTheme, size),
                            ],
                            const YMargin(100), // Bottom padding for FAB
                          ]),
                        ),
                      );
                    },
                    loading: () {
                      return SliverPadding(
                        padding: EdgeInsets.only(
                          top: 20,
                          left: size.width * 0.05,
                          right: size.width * 0.05,
                        ),
                        sliver: SliverSkeletonizer(
                          enabled: true,
                          child: SliverList(
                            delegate: SliverChildListDelegate([
                              const YMargin(20),
                              // Dashboard Statistics Skeleton
                              _buildDashboardStats(
                                  context,
                                  [Car.defaultValue()],
                                  textTheme,
                                  colorScheme,
                                  ref),
                              const YMargin(24),
                              // Quick Actions Skeleton
                              _buildQuickActions(context, size),
                              const YMargin(24),
                              // Car Card Skeleton
                              CarCard(Car.defaultValue(), home: true),
                              const YMargin(100),
                            ]),
                          ),
                        ),
                      );
                    },
                    error: (e, s) {
                      return SliverToBoxAdapter(
                        child: FailureWidget(
                          fullScreen: true,
                          e: e,
                          retry: () async {
                            await ref
                                .read(fleetControllerProvider.notifier)
                                .loadCars();
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
        drawer: const CustomDrawer(),
      ),
    );
  }

  Widget _buildDashboardStats(BuildContext context, List<Car> cars,
      TextTheme textTheme, ColorScheme colorScheme, WidgetRef ref) {
    final user = ref.read(authControllerProvider).user;
    final userMetaData = user?.metaData;

    // Use UserMetaData if available, otherwise fallback to car calculations
    final totalCars = userMetaData?.totalCars ?? cars.length;
    final availableCars = userMetaData?.availableCars ??
        cars.where((car) => car.isAvailable).length;
    final totalBookings = userMetaData?.totalBookings ??
        cars.fold<int>(0, (sum, car) => sum + car.bookingCount);
    final totalEarnings =
        cars.fold<num>(0, (sum, car) => sum + car.totalEarned);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
            color: colorScheme.secondary.withAlpha(51)), // 0.2 opacity
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Fleet Overview',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.secondary,
            ),
          ),
          const YMargin(16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Cars',
                  totalCars.toString(),
                  Icons.directions_car,
                  Colors.blue,
                  textTheme,
                ),
              ),
              const XMargin(12),
              Expanded(
                child: _buildStatCard(
                  'Available',
                  availableCars.toString(),
                  Icons.check_circle,
                  Colors.green,
                  textTheme,
                ),
              ),
            ],
          ),
          const YMargin(12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Bookings',
                  totalBookings.toString(),
                  Icons.event,
                  Colors.orange,
                  textTheme,
                ),
              ),
              const XMargin(12),
              Expanded(
                child: _buildStatCard(
                  'Total Earnings',
                  CurrencyItem.formatAmount(context, totalEarnings),
                  Icons.attach_money,
                  Colors.purple,
                  textTheme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    TextTheme textTheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(26), // 0.1 opacity
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const YMargin(8),
          Text(
            value,
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const YMargin(4),
          Text(
            title,
            style: textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const YMargin(16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Add Car',
                Icons.add_circle_outline,
                Colors.green,
                () => context.pushNamed(addCarPath),
              ),
            ),
            const XMargin(12),
            Expanded(
              child: _buildActionCard(
                'View Bookings',
                Icons.event_note,
                Colors.blue,
                () => context.pushNamed(bookingsPath),
              ),
            ),
          ],
        ),
        const YMargin(12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'My Cars',
                Icons.garage,
                Colors.orange,
                () => context.pushNamed(carListingPath),
              ),
            ),
            const XMargin(12),
            Expanded(
              child: _buildActionCard(
                'Profile',
                Icons.person,
                Colors.purple,
                () => context.pushNamed(updateProfilePath),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: color.withAlpha(77)), // 0.3 opacity
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const YMargin(8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(
      BuildContext context, TextTheme textTheme, Size size) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const YMargin(40),
        Icon(
          Icons.directions_car_outlined,
          size: 80,
          color: Theme.of(context)
              .colorScheme
              .secondary
              .withAlpha(128), // 0.5 opacity
        ),
        const YMargin(20),
        Text(
          'You have not uploaded any cars yet',
          style: textTheme.titleLarge?.copyWith(
            color: Theme.of(context)
                .colorScheme
                .onSurface
                .withAlpha(204), // 0.8 opacity
          ),
          textAlign: TextAlign.center,
        ),
        const YMargin(30),
        LuxButton(
          onTap: () => context.pushNamed(addCarPath),
          text: 'Upload Your First Car',
          icon: const Icon(Icons.add_circle_outline),
          width: size.width * 0.8,
        ),
      ],
    );
  }
}

class AppBarMenu extends StatelessWidget {
  const AppBarMenu({super.key});

  @override
  build(BuildContext context) {
    return InkWell(
      onTap: () {
        HomeScreenState.drawerKey.currentState?.openDrawer();
      },
      child: const Icon(
        Icons.menu,
        size: 24,
      ),
    );
  }
}
