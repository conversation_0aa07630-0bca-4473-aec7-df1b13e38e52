import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'fleet_params.dart';

final fleetRepositoryProvider = Provider<FleetRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return LuxFleetRepository(dio);
});

abstract class FleetRepository {
  Future<Car> createCar(Map<String, dynamic> params);
  Future<Car> getCar(String carId);
  Future<List<Car>> getCars();
  Future<BrandAndCategoryResult> getBrandsAndCategories();
  Future<Unit> updateCar(UpdateCarParams params);
  Future<Unit> delistCar(String carId);
  Future<Unit> updateImageVisibility(ImageVisibilityParams params);
  Future<Unit> changeMainImage(String carId, String imageId);
  Future<Unit> deleteImage(String carId, String imageId);
}

class LuxFleetRepository implements FleetRepository {
  LuxFleetRepository(this.dio);

  final Dio dio;

  @override
  Future<Car> createCar(Map<String, dynamic> params) async {
    return await dioInterceptor(() async {
      final res = await dio.post('cars', data: params);
      final data = Map<String, dynamic>.from(res.data['data']);
      final car = Car.fromMap(data);
      return car;
    });
  }

  @override
  Future<BrandAndCategoryResult> getBrandsAndCategories() async {
    return await dioInterceptor(() async {
      final res = await dio.get('cars/brands-and-categories');
      final data = Map<String, dynamic>.from(res.data['data']);
      final bc = BrandAndCategoryResult.fromMap(data);
      return bc;
    });
  }

  @override
  Future<Car> getCar(String carId) async {
    return await dioInterceptor(() async {
      final res = await dio.get('cars/$carId');
      final car = res.data['data'];
      return Car.fromMap(car);
    });
  }

  @override
  Future<List<Car>> getCars() async {
    return await dioInterceptor(() async {
      final res = await dio.get('cars');
      final List<dynamic> cars = res.data['data']['results'];
      return cars.map((x) => Car.fromMap(x)).toList();
    });
  }

  @override
  Future<Unit> updateCar(UpdateCarParams params) async {
    return await dioInterceptor(() async {
      await dio.patch('cars/${params.carId}', data: params.data);
      return unit;
    });
  }

  @override
  Future<Unit> delistCar(String carId) async {
    return await dioInterceptor(() async {
      await dio.delete('cars/$carId');
      return unit;
    });
  }

  @override
  Future<Unit> updateImageVisibility(ImageVisibilityParams params) async {
    return await dioInterceptor(() async {
      await dio.put('cars/images', data: [params.toMap()]);
      return unit;
    });
  }

  @override
  Future<Unit> changeMainImage(String carId, String imageId) async {
    return await dioInterceptor(() async {
      await dio.patch('cars/images/$carId', data: {'imageId': imageId});
      return unit;
    });
  }

  @override
  Future<Unit> deleteImage(String carId, String imageId) async {
    return await dioInterceptor(() async {
      await dio.delete('cars/images/$carId', data: {
        'imageIds': [imageId]
      });
      return unit;
    });
  }
}
