import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/booking/booking_controller.dart';
import 'package:fleet_mobile/features/booking/booking_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';

class BookingCard extends ConsumerWidget {
  BookingCard({
    super.key,
    required this.booking,
  });

  final Booking booking;

  late final _booking = ValueNotifier(booking);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;
    final size = MediaQuery.of(context).size;

    return ValueListenableBuilder<Booking>(
      valueListenable: _booking,
      builder: (context, booking, _) {
        return GestureDetector(
          onTap: () {
            ref
                .read(bookingControllerProvider.notifier)
                .updateBookingInView(booking);
            context.pushNamed(bookingDetailPath);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5),
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: colorScheme.secondary,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.brightness == Brightness.light
                        ? colorScheme.secondary.withValues(alpha: 30)
                        : colorScheme.secondary.withValues(alpha: 77),
                    blurRadius: theme.brightness == Brightness.light ? 2 : 1,
                    spreadRadius: theme.brightness == Brightness.light ? 0 : 1,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const YMargin(15),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Skeleton.shade(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15),
                          child: booking.car.imageUrl == null
                              ? Container(
                                  width: size.width * 0.35,
                                  height: size.width * 0.25,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: colorScheme.onSurface
                                          .withValues(alpha: 77), // 0.3 opacity
                                    ),
                                    borderRadius: BorderRadius.circular(15),
                                    color: colorScheme.surface
                                        .withValues(alpha: 128), // 0.5 opacity
                                  ),
                                  child: Center(
                                    child: Icon(
                                      Icons.directions_car,
                                      color: colorScheme.onSurface,
                                      size: 40,
                                    ),
                                  ),
                                )
                              : Container(
                                  width: size.width * 0.35,
                                  height: size.width * 0.25,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: colorScheme.onSurface
                                          .withValues(alpha: 77), // 0.3 opacity
                                    ),
                                    borderRadius: BorderRadius.circular(15),
                                    image: DecorationImage(
                                      fit: BoxFit.cover,
                                      image:
                                          NetworkImage(booking.car.imageUrl!),
                                    ),
                                  ),
                                ),
                        ),
                      ),
                      // Client information section - positioned under car image
                      // Only show for approved bookings
                      if (booking.isAccepted) ...[
                        const YMargin(12),
                        Container(
                          width: size.width * 0.35,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: colorScheme.surface,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: colorScheme.secondary.withAlpha(51),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Client Info',
                                style: textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.secondary,
                                ),
                              ),
                              const YMargin(8),
                              _buildClientInfo(textTheme, colorScheme),
                            ],
                          ),
                        ),
                      ],
                      const XMargin(20),
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const YMargin(6),
                            Text(
                              'Date requested: ${booking.createdAt.toShortDate()}',
                              style: textTheme.bodyMedium?.copyWith(
                                color: colorScheme.secondary,
                              ),
                            ),
                            const YMargin(14),
                            // Show approval status if available
                            if (booking.hasApproval) ...[
                              Text(
                                capitalize(booking.approvalStatus == 'rejected'
                                    ? 'declined'
                                    : booking.approvalStatus),
                                style: textTheme.titleLarge?.copyWith(
                                  color: booking.isAccepted
                                      ? Colors.green
                                      : theme.colorScheme.error,
                                ),
                              ),
                              const YMargin(12),
                            ],
                            // Always show pickup and drop-off addresses for all bookings
                            _buildAddressInfo(
                              'Pickup',
                              booking.pickupAddress.address ?? 'N/A',
                              Icons.location_on,
                              Colors.green,
                              textTheme,
                            ),
                            const YMargin(8),
                            _buildAddressInfo(
                              'Drop-off',
                              booking.destinationAddress.address ?? 'N/A',
                              Icons.location_off,
                              Colors.red,
                              textTheme,
                            ),
                            // Show action buttons for pending bookings
                            if (!booking.hasApproval) ...[
                              const YMargin(16),
                              Row(
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.check_circle,
                                        color: Colors.green, size: 40),
                                    onPressed: () {
                                      showConfirmationDialog(
                                        context,
                                        'Accept Booking',
                                        'Are you sure you want to accept this booking?',
                                        'Accept',
                                        actionTextColor: Colors.green,
                                        onConfirm: () async {
                                          Navigator.of(context).pop();

                                          Loader.show(context);

                                          final res = await ref
                                              .read(bookingServiceProvider)
                                              .approveBooking(booking.id);

                                          res.when(
                                            (data) {
                                              ref
                                                  .read(
                                                      bookingControllerProvider
                                                          .notifier)
                                                  .updateBookingInState(data);
                                              _booking.value = data;
                                              Toast.success(
                                                  'Booking Accepted Successfully');
                                              Loader.hide();
                                            },
                                            (error) {
                                              Toast.error(error.message);
                                              Loader.hide();
                                            },
                                          );
                                        },
                                      );
                                    },
                                  ),
                                  const XMargin(20),
                                  IconButton(
                                    icon: const Icon(Icons.cancel,
                                        color: Colors.red, size: 40),
                                    onPressed: () {
                                      showConfirmationDialog(
                                        context,
                                        'Decline Booking',
                                        'Are you sure you want to decline this booking?',
                                        'Decline',
                                        actionTextColor: Colors.red,
                                        onConfirm: () async {
                                          Navigator.of(context).pop();

                                          Loader.show(context);

                                          final res = await ref
                                              .read(bookingServiceProvider)
                                              .declineBooking(booking.id);

                                          res.when(
                                            (data) {
                                              ref
                                                  .read(
                                                      bookingControllerProvider
                                                          .notifier)
                                                  .updateBookingInState(data);
                                              _booking.value = data;
                                              Toast.success(
                                                  'Booking Declined Successfully');
                                              Loader.hide();
                                            },
                                            (error) {
                                              Toast.error(error.message);
                                              Loader.hide();
                                            },
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddressInfo(
    String label,
    String address,
    IconData icon,
    Color iconColor,
    TextTheme textTheme,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: iconColor,
        ),
        const XMargin(8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: iconColor,
                ),
              ),
              Text(
                address,
                style: textTheme.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildClientInfo(TextTheme textTheme, ColorScheme colorScheme) {
    // Note: This is placeholder content. In a real implementation,
    // you would need to add client information to the Booking model
    // and fetch it from the API
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.person,
              size: 12,
              color: colorScheme.secondary,
            ),
            const XMargin(4),
            Expanded(
              child: Text(
                'Contact Support',
                style: textTheme.bodySmall?.copyWith(fontSize: 10),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const YMargin(4),
        Row(
          children: [
            Icon(
              Icons.phone,
              size: 12,
              color: colorScheme.secondary,
            ),
            const XMargin(4),
            Expanded(
              child: Text(
                'Contact Support',
                style: textTheme.bodySmall?.copyWith(fontSize: 10),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
