import 'package:fleet_mobile/core/core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'booking_service.dart';
import 'booking_state.dart';

final bookingControllerProvider =
    StateNotifierProvider<BookingController, BookingState>(
  (ref) {
    final bookingService = ref.watch(bookingServiceProvider);
    return BookingController(
      BookingState.initial(),
      bookingService,
    );
  },
);

class BookingController extends StateNotifier<BookingState> {
  BookingController(
    super.state,
    this._bookingService,
  );

  final BookingService _bookingService;

  Future<void> loadBookings(String id, [int retry = 3]) async {
    state = state.copyWith(bookings: const AsyncValue.loading());

    final res = await _bookingService.getBookings(id);
    res.when(
      (bookings) {
        state = state.copyWith(bookings: AsyncValue.data(bookings));
      },
      (error) async {
        if (retry > 0) {
          await Future.delayed(
              const Duration(milliseconds: retryDelayMilliseconds));
          loadBookings(id, retry - 1);
        } else {
          state = state.copyWith(
            bookings: AsyncValue.error(error, StackTrace.current),
          );
        }
      },
    );
  }

  void updateBookingInView(Booking booking) {
    state = state.copyWith(bookingInView: booking);
  }

  void updateBookingInState(Booking booking) {
    state = state.copyWith(
      bookings: AsyncValue.data(
        state.bookings.value!
            .map((x) => x.id == booking.id ? booking : x)
            .toList(),
      ),
    );
  }
}
