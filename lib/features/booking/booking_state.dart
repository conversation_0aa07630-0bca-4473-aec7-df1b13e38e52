import 'package:equatable/equatable.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

@immutable
class BookingState extends Equatable {
  final Booking? bookingInView;
  final AsyncValue<List<Booking>> bookings;

  const BookingState({
    this.bookingInView,
    this.bookings = const AsyncValue.data([]),
  });

  factory BookingState.initial() {
    return const BookingState(
      bookingInView: null,
    );
  }

  BookingState copyWith({
    Booking? bookingInView,
    AsyncValue<List<Booking>>? bookings,
  }) {
    return BookingState(
      bookingInView: bookingInView ?? this.bookingInView,
      bookings: bookings ?? this.bookings,
    );
  }

  @override
  List<Object?> get props => [bookingInView, bookings];
}
