import 'package:fleet_mobile/core/core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:multiple_result/multiple_result.dart';

import 'booking_repository.dart';

final bookingServiceProvider = Provider<BookingService>((ref) {
  final bookingRepository = ref.read(bookingRepositoryProvider);
  return LuxBookingService(
    bookingRepository,
    ref,
  );
});

abstract class BookingService {
  Future<Result<List<Booking>, Failure>> getBookings(String id);
  Future<Result<Booking, Failure>> approveBooking(String id);
  Future<Result<Booking, Failure>> declineBooking(String id);
}

class LuxBookingService implements BookingService {
  final BookingRepository _bookingRepository;
  final Ref _ref;

  LuxBookingService(this._bookingRepository, this._ref);

  @override
  Future<Result<List<Booking>, Failure>> getBookings(String id) async {
    try {
      final res = await _bookingRepository.getBookings(id);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Booking, Failure>> approveBooking(String id) async {
    try {
      final res = await _bookingRepository.approveBooking(id);
      // Stop persistent notification for this booking
      _ref.read(pushNotificationProvider).stopBookingNotification(id);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }

  @override
  Future<Result<Booking, Failure>> declineBooking(String id) async {
    try {
      final res = await _bookingRepository.declineBooking(id);
      // Stop persistent notification for this booking
      _ref.read(pushNotificationProvider).stopBookingNotification(id);
      return Success(res);
    } on Failure catch (e) {
      return Error(e);
    }
  }
}
