import 'package:dio/dio.dart';
import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/core/utils/providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final bookingRepositoryProvider = Provider<BookingRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return LuxBookingRepository(dio);
});

abstract class BookingRepository {
  Future<List<Booking>> getBookings(String id);
  Future<Booking> approveBooking(String id);
  Future<Booking> declineBooking(String id);
}

class LuxBookingRepository implements BookingRepository {
  LuxBookingRepository(this.dio);

  final Dio dio;

  @override
  Future<List<Booking>> getBookings(String id) async {
    return await dioInterceptor(() async {
      final res = await dio.get('bookings/manager');
      final List<dynamic> bookings = res.data['data'];
      return bookings.map((x) => Booking.fromMap(x)).toList();
    });
  }

  @override
  Future<Booking> approveBooking(String id) async {
    return await dioInterceptor(() async {
      final res =
          await dio.post('bookings/approve/$id', data: {'accept': true});
      return Booking.fromMap(res.data['data']);
    });
  }

  @override
  Future<Booking> declineBooking(String id) async {
    return await dioInterceptor(() async {
      final res =
          await dio.post('bookings/decline/$id', data: {'accept': false});
      return Booking.fromMap(res.data['data']);
    });
  }
}
