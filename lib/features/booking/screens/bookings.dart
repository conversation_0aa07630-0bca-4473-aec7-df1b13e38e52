import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/booking/booking_controller.dart';
import 'package:fleet_mobile/features/booking/widgets/widgets.dart';
import 'package:fleet_mobile/theme/palette.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class BookingsScreen extends ConsumerStatefulWidget {
  const BookingsScreen({super.key});

  @override
  ConsumerState<BookingsScreen> createState() => _BookingsScreenState();
}

class _BookingsScreenState extends ConsumerState<BookingsScreen> {
  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      await _fetchBookings();
    });
  }

  Future<void> _fetchBookings() async {
    final managerId = ref.read(authControllerProvider).user?.id;
    if (managerId != null) {
      await ref
          .read(bookingControllerProvider.notifier)
          .loadBookings(managerId);
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final bookings = ref.watch(bookingControllerProvider).bookings;

    return SafeArea(
      child: Scaffold(
        body: RefreshIndicator(
          color: Palette.kFFB800,
          onRefresh: () async {
            await _fetchBookings();
          },
          child: CustomScrollView(
            slivers: [
              const SliverAppBar(
                leading: BackButton(),
              ),
              SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 18.0),
                      child: Text(
                        'All Bookings',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const YMargin(10),
                  ],
                ),
              ),
              bookings.when(
                data: (data) {
                  if (data.isEmpty) {
                    return SliverFillRemaining(
                      hasScrollBody: false,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Stack(
                              alignment: Alignment.center,
                              children: [
                                Icon(
                                  Icons.calendar_today_outlined,
                                  size: 80,
                                  color: Theme.of(context)
                                      .primaryColorLight
                                      .withValues(alpha: 0.5),
                                ),
                                Positioned(
                                  right: 0,
                                  bottom: 0,
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.directions_car_outlined,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const YMargin(24),
                            Text(
                              'No Bookings Yet',
                              style: textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const YMargin(8),
                            Text(
                              'When customers book your cars,\nthey will appear here',
                              style: textTheme.bodyLarge?.copyWith(
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  return bookingList(data);
                },
                loading: () {
                  return Skeletonizer.sliver(
                      child: bookingList(List.filled(
                    2,
                    Booking.defaultValue(),
                  )));
                },
                error: (e, s) {
                  return SliverToBoxAdapter(
                    child: FailureWidget(
                        fullScreen: true, e: e, retry: _fetchBookings),
                  );
                },
              ),
              // SliverList(
              //   delegate: SliverChildListDelegate(
              //     [
              //       Padding(
              //         padding: const EdgeInsets.only(left: 18.0),
              //         child: Text(
              //           'All Bookings',
              //           style: textTheme.titleLarge?.copyWith(
              //             fontWeight: FontWeight.w600,
              //           ),
              //         ),
              //       ),
              //       const YMargin(10),
              //       BookingCard(
              //         booking: Booking(
              //             id: 1,
              //             userId: 1,
              //             totalPrice: 150000,
              //             createdAt: DateTime.now()),
              //       ),
              //       BookingCard(
              //         booking: Booking(
              //             id: 2,
              //             userId: 2,
              //             totalPrice: 100000,
              //             createdAt: DateTime.now().add(const Duration(days: -2))),
              //       ),
              //     ],
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget bookingList(List<Booking> data) {
    return SliverList.builder(
      itemBuilder: (_, index) {
        final booking = data[index];
        return BookingCard(booking: booking);
      },
      itemCount: data.length,
    );
  }
}
