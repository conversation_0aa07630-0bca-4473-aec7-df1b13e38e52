import 'package:fleet_mobile/core/core.dart';
import 'package:fleet_mobile/features/auth/auth_controller.dart';
import 'package:fleet_mobile/features/booking/booking_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BookingDetailScreen extends ConsumerWidget {
  const BookingDetailScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final booking = ref.watch(bookingControllerProvider).bookingInView!;
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Booking Details'),
          leading: const BackButton(),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: BookingSummaryCard(booking: booking),
        ),
      ),
    );
  }
}

class BookingSummaryCard extends ConsumerWidget {
  const BookingSummaryCard({super.key, required this.booking});

  final Booking booking;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userEmail = ref.watch(authControllerProvider).user?.email;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              booking.car.name,
              style:
                  textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Divider(),
            SummaryTile(
              title: 'Booking Fee',
              body: CurrencyItem.formatAmountWithSymbol(
                  context, booking.totalPrice),
            ),
            SummaryTile(
              title: 'Ride',
              body: booking.car.name,
            ),
            SummaryTile(
              title: 'Booking Type',
              body: booking.bookingType,
            ),
            SummaryTile(
              title: 'Number of Cars',
              body: booking.carCount.toString(),
            ),
            if (booking.escortCount > 0)
              SummaryTile(
                title: 'Police Escorts',
                body: booking.escortCount.toString(),
              ),
            SummaryTile(
              title: 'Booking Style',
              body: booking.car.schedule?.name ?? '-',
            ),
            SummaryTile(
              title: 'Pickup Date',
              body: booking.startDate.toShortDate(),
            ),
            SummaryTile(
              title: 'Pickup Time',
              body: booking.startTime,
            ),
            SummaryTile(
              title: 'Drop-off Date',
              body: booking.endDate.toShortDate(),
            ),
            SummaryTile(
              title: 'Pickup Address',
              body: booking.pickupAddress.address ?? '-',
            ),
            SummaryTile(
              title: 'Destination Address',
              body: booking.destinationAddress.address ?? '-',
            ),
            if (userEmail != null && userEmail.isNotEmpty)
              SummaryTile(
                title: 'Email Address',
                body: userEmail,
              ),
            const Divider(),
          ],
        ),
      ),
    );
  }
}

class SummaryTile extends StatelessWidget {
  const SummaryTile({
    super.key,
    required this.title,
    required this.body,
  });

  final String title;
  final String body;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              title,
              style: textTheme.titleMedium,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 3,
            child: Text(
              body,
              style:
                  textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}

// class BookingDetailScreen extends ConsumerWidget {
//   const BookingDetailScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final Booking booking = ref.watch(bookingControllerProvider).bookingInView!;

//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Booking Details'),
//         leading: const BackButton(),
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: CustomScrollView(
//           slivers: [
//             SliverToBoxAdapter(
//               child: BookingSummaryWidget(booking),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// class BookingSummaryWidget extends ConsumerWidget {
//   const BookingSummaryWidget(this.booking, {super.key});

//   final Booking booking;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final authProvider = ref.watch(authControllerProvider);
//     final textTheme = Theme.of(context).textTheme;

//     final emailAddress = authProvider.user?.email;

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Booking Details',
//           style: textTheme.titleLarge?.copyWith(
//             color: Colors.black,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         const SizedBox(height: 20),
//         SummaryItemWidget(
//           title: 'Booking Fee',
//           body: CurrencyItem.formatAmountWithSymbol(
//               context, booking.totalPrice),
//         ),
//         SummaryItemWidget(
//           title: 'Ride',
//           body: booking.car.name,
//         ),
//         SummaryItemWidget(
//           title: 'Booking type',
//           body: booking.bookingType,
//         ),
//         SummaryItemWidget(
//           title: 'Number of cars',
//           body: booking.carCount.toString(),
//         ),
//         Visibility(
//           visible: booking.escortCount > 0,
//           child: SummaryItemWidget(
//             title: 'Number of police escorts',
//             body: '${booking.escortCount}',
//           ),
//         ),
//         SummaryItemWidget(
//           title: 'Booking style',
//           body: booking.car.schedule.name,
//         ),
//         SummaryItemWidget(
//           title: 'Pickup date',
//           body: booking.startDate.toShortDate(),
//         ),
//         SummaryItemWidget(
//           title: 'Pickup time',
//           body: booking.startTime,
//         ),
//         SummaryItemWidget(
//           title: 'Drop-off date',
//           body: booking.endDate.toShortDate(),
//         ),
//         SummaryItemWidget(
//           title: 'Pickup address',
//           body: '${booking.pickupAddress.address}',
//         ),
//         SummaryItemWidget(
//           title: 'Destination address',
//           body: '${booking.destinationAddress.address}',
//         ),
//         if (emailAddress != null && emailAddress.isNotEmpty)
//           SummaryItemWidget(
//             title: 'Email address',
//             body: emailAddress,
//           ),
//       ],
//     );
//   }
// }

// class SummaryItemWidget extends StatelessWidget {
//   const SummaryItemWidget({
//     super.key,
//     required this.title,
//     required this.body,
//   });

//   final String title;
//   final String body;

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 14),
//       child: Row(
//         children: [
//           Expanded(
//             flex: 1,
//             child: Text(title, style: textTheme.bodyMedium),
//           ),
//           Expanded(
//             flex: 1,
//             child: Text(
//               body,
//               style: textTheme.bodyMedium?.copyWith(
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
