// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_prod.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBnIF0r3_LAYFcvR3gPKJ4qbt2YurqSor0',
    appId: '1:450830351923:android:d2169b39dcfef8d2c82d46',
    messagingSenderId: '450830351923',
    projectId: 'secapay-pos',
    databaseURL: 'https://secapay-pos.firebaseio.com',
    storageBucket: 'secapay-pos.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyChtFdZGOFhxti1l9rvnyEu7D9Xt1i-csc',
    appId: '1:450830351923:ios:15406de627d3b475c82d46',
    messagingSenderId: '450830351923',
    projectId: 'secapay-pos',
    databaseURL: 'https://secapay-pos.firebaseio.com',
    storageBucket: 'secapay-pos.firebasestorage.app',
    androidClientId: '450830351923-uiup8ps9oh8id9ec5f32hg5jmd7c62t2.apps.googleusercontent.com',
    iosBundleId: 'com.luxlet.fleetmgr',
  );
}
