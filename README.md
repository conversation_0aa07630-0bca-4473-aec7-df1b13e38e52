# LuxLet Fleet Mobile

A comprehensive fleet management mobile application built with Flutter.

## Overview

LuxLet Fleet is a mobile application designed for fleet managers to efficiently manage their vehicle fleet, handle bookings, and administer user accounts. The app provides a seamless experience for managing car listings, tracking bookings, and handling administrative tasks.

## Features

- **Authentication**: Secure login, signup, and password management
- **Fleet Management**: Add, update, and manage vehicle listings
- **Booking Management**: View and manage booking requests
- **Admin Dashboard**: User management and administrative controls
- **Image Management**: Upload and manage vehicle images
- **Push Notifications**: Real-time updates for booking requests and system events

## Getting Started

### Prerequisites

- Flutter SDK (latest stable version)
- Dart SDK
- Android Studio / Xcode for emulators
- Firebase project setup

### Installation

1. Clone the repository

   ```bash
   git clone https://github.com/yourusername/LuxLet-fleet_mobile.git
   cd fleet_mobile
   ```

2. Install dependencies

   ```bash
   flutter pub get
   ```

3. Run the app
   ```bash
   flutter run
   ```

## Project Structure

The project follows a feature-first architecture. For detailed information about the architecture, see [ARCHITECTURE.md](ARCHITECTURE.md).

## Environment Configuration

The app supports multiple environments (development and production) with different Firebase configurations.

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

This project is licensed under the [LICENSE NAME] - see the LICENSE file for details.
